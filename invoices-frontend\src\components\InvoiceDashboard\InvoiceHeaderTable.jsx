import React, { useMemo, useEffect, useState } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import FilterButton from './FilterButton';

const headerRowClassName = (rowData) => {
    if (rowData.processingStatus === "Ignored") return 'ignored-header-row';
    const highlightedStatuses = ["Processed", "Override", "Completed"];
    return highlightedStatuses.includes(rowData.processingStatus) ? 'highlighted-header-row' : '';
};

const statusIndicatorTemplate = (rowData) => {
    const getStatusIcon = () => {
        switch (rowData.processingStatus?.toLowerCase()) {
            case 'processing':
                return <i className="pi pi-arrow-right" style={{ color: '#17a2b8', fontSize: '14px' }} title="Processing" />;
            case 'error':
                return <i className="pi pi-times" style={{ color: '#dc3545', fontSize: '14px' }} title="Error" />;
            case 'processed':
                return <i className="pi pi-check" style={{ color: '#28a745', fontSize: '14px' }} title="Processed" />;
            case 'pending':
                return <i className="pi pi-clock" style={{ color: '#ffc107', fontSize: '14px' }} title="Pending" />;
            case 'cancelled':
            case 'ignored':
                return <i className="pi pi-ban" style={{ color: '#6c757d', fontSize: '14px' }} title="Cancelled/Ignored" />;
            case 'override':
                return <i className="pi pi-exclamation-triangle" style={{ color: '#fd7e14', fontSize: '14px' }} title="Override" />;
            case 'staged':
                return <i className="pi pi-circle" style={{ color: '#6c757d', fontSize: '12px' }} title="Pending" />;
            case 'completed':
                return <i className="pi pi-lock" style={{ color: '#6c757d', fontSize: '12px' }} title="Completed" />;
            default:
                return <i className="pi pi-circle" style={{ color: '#6c757d', fontSize: '12px' }} title="Unknown" />;
        }
    };

    return (
        <div style={{ textAlign: 'center' }}>
            {getStatusIcon(rowData.status)}
        </div>
    );
};

// Date formatting function
const formatDateOnly = (dateValue) => {
    if (!dateValue) return '';
    
    try {
        const date = new Date(dateValue);
        if (isNaN(date.getTime())) return dateValue; // Return original if invalid date
        
        // Format as YYYY-MM-DD or MM/DD/YYYY - choose your preferred format
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        });
    } catch  {
        return dateValue; // Return original value if formatting fails
    }
};

const InvoiceHeaderTable = ({
    invoices,
    loading,
    selectedId,
    onSelectionChange,
    onNotesClick,
    filters,
    updateFilter,
    clearFilter,
    disabled = false
}) => {
    const [currentPage, setCurrentPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(10);

    // Local filtering for invoice number only
    const filteredInvoices = useMemo(() => {
        if (!filters?.invoiceNumber || filters.invoiceNumber.trim() === '') {
            return invoices;
        }
        
        return invoices.filter(invoice => 
            invoice.invoiceNumber?.toString().toLowerCase().includes(filters.invoiceNumber.toLowerCase())
        );
    }, [invoices, filters?.invoiceNumber]);

    // Only unselect if the record doesn't exist in the filtered data
    useEffect(() => {
        if (selectedId && filteredInvoices.length > 0) {
            const recordExistsInFilteredData = filteredInvoices.some(invoice => invoice.headerID === selectedId);
            
            if (!recordExistsInFilteredData) {
                // Record doesn't exist in filtered data - unselect
                onSelectionChange(null);
            }
        }
    }, [selectedId, filteredInvoices, onSelectionChange]);

    // Reset to first page when filters change
    useEffect(() => {
        setCurrentPage(0);
    }, [filters?.invoiceNumber]);

    // Reset to first page when rows per page changes
    useEffect(() => {
        setCurrentPage(0);
    }, [rowsPerPage]);

    // Custom paginator template for more control
    const paginatorTemplate = {
        layout: 'FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown CurrentPageReport JumpToPageInput',
        RowsPerPageDropdown: (options) => {
            const dropdownOptions = [
                { label: '10', value: 10 },
                { label: '25', value: 25 },
                { label: '50', value: 50 },
                { label: '100', value: 100 }
            ];

            return (
                <div className="flex align-items-center gap-2" style={{ margin: '0 1rem' }}>
                    <span style={{ fontSize: '0.875rem', whiteSpace: 'nowrap' }}>Rows per page:</span>
                    <select
                        value={options.value}
                        onChange={(e) => {
                            const newRows = parseInt(e.target.value);
                            setRowsPerPage(newRows);
                            options.onChange({ rows: newRows });
                        }}
                        className="p-inputtext p-component"
                        style={{ width: '70px', padding: '0.25rem', fontSize: '0.875rem' }}
                        disabled={disabled}
                    >
                        {dropdownOptions.map(option => (
                            <option key={option.value} value={option.value}>
                                {option.label}
                            </option>
                        ))}
                    </select>
                </div>
            );
        },
        CurrentPageReport: (options) => {
            // Use the props object which has the correct values
            const actualOptions = options.props || options;
            const start = actualOptions.first + 1;
            const end = Math.min(actualOptions.first + actualOptions.rows, actualOptions.totalRecords);
            
            // Handle edge case where there are no records
            if (actualOptions.totalRecords === 0) {
                return (
                    <span style={{ fontSize: '0.875rem', color: '#6c757d', margin: '0 1rem', whiteSpace: 'nowrap' }}>
                        Showing 0 to 0 of 0 entries
                    </span>
                );
            }
            
            return (
                <span style={{ fontSize: '0.875rem', color: '#6c757d', margin: '0 1rem', whiteSpace: 'nowrap' }}>
                    Showing {start} to {end} of {actualOptions.totalRecords} entries
                </span>
            );
        },
        JumpToPageInput: (options) => {
            // Use the props object which has the correct values
            const actualOptions = options.props || options;
            
            // Only show if there are multiple pages
            if (actualOptions.totalPages <= 1) return null;

            return (
                <div className="flex align-items-center gap-2" style={{ margin: '0 1rem' }}>
                    <span style={{ fontSize: '0.875rem', whiteSpace: 'nowrap' }}>Go to page:</span>
                    <input
                        type="number"
                        min="1"
                        max={actualOptions.totalPages}
                        value={actualOptions.page + 1}
                        onChange={(e) => {
                            const newPage = parseInt(e.target.value) - 1;
                            if (newPage >= 0 && newPage < actualOptions.totalPages) {
                                setCurrentPage(newPage);
                                options.onChange({ page: newPage });
                            }
                        }}
                        className="p-inputtext p-component"
                        style={{ width: '60px', padding: '0.25rem', fontSize: '0.875rem' }}
                        disabled={disabled}
                    />
                    <span style={{ fontSize: '0.875rem', color: '#6c757d' }}>of {actualOptions.totalPages}</span>
                </div>
            );
        }
    };

    return (
        <div className="card mx-4" style={{ minHeight: "360px" }}>
            <DataTable
                value={filteredInvoices}
                selectionMode="single"
                selection={filteredInvoices.find(h => h.headerID === selectedId) || null}
                onSelectionChange={(e) => onSelectionChange(e.value?.headerID)}
                dataKey="headerID"
                loading={loading}
                rowClassName={headerRowClassName}
                size="small"
                paginator 
                rows={rowsPerPage}
                first={currentPage * rowsPerPage}
                onPage={(event) => {
                    setCurrentPage(event.page);
                    // Don't call handlePageChange separately, this should be enough
                }}
                rowsPerPageOptions={[10, 25, 50, 100]}
                paginatorTemplate={paginatorTemplate}
                totalRecords={filteredInvoices.length}
                scrollable 
                scrollHeight="300px"
                emptyMessage={<div style={{ height: '270px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>No records found.</div>}
            >
                <Column
                    header=""
                    body={statusIndicatorTemplate}
                    style={{ width: '40px', textAlign: 'center' }}
                />
                <Column
                    field="hasNotes"
                    header="Notes"
                    body={(rowData) => rowData.hasNotes ? <i className="pi pi-file" style={{ color: '#007bff', cursor: 'pointer' }} onClick={() => onNotesClick(rowData.headerID)} /> : null}
                    style={{ width: '50px', textAlign: 'center' }}
                />
                <Column field="externalCounterparty" header="External Counterparty" sortable />
                <Column field="internalCounterparty" header="Internal Counterparty" sortable />
                <Column field="template" header="Template" />
                <Column field="invoiceNumber" header={<FilterButton label="Invoice #" filterKey="invoiceNumber" filters={filters} updateFilter={updateFilter} clearFilter={clearFilter} />} sortable />
                <Column field="invoiceDate" header="Invoice Date" body={(rowData) => formatDateOnly(rowData.invoiceDate)} sortable />
                <Column field="dueDate" header="Due Date" body={(rowData) => formatDateOnly(rowData.dueDate)} sortable />
                <Column field="paymentTerms" header="Payment Terms" />
                <Column field="totalAmount" header="Total Amount" body={(rowData) => `$${Number(rowData.totalAmount).toFixed(2)}`} />
                <Column field="status" header="Status" />
            </DataTable>
        </div>
    );
};

export default InvoiceHeaderTable;