// src/api/apiService.js - Updated to work with <PERSON><PERSON> hooks
import environment from "./environment";

class ApiService {
  constructor() {
    this.baseUrl = environment.apiBaseUrl;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };
  }

  async request(endpoint, method = 'GET', body = null, customHeaders = {}, authHeadersProvider = null) {
    const url = `${this.baseUrl}${endpoint}`;
    
    let authHeaders = {};
    
    if (authHeadersProvider) {
      authHeaders = await authHeadersProvider();
    }
    
    const headers = { 
      ...this.defaultHeaders, 
      ...authHeaders, 
      ...customHeaders 
    };

    const options = {
      method,
      headers
    };

    if (body) {
      options.body = JSON.stringify(body);
    }

    const response = await fetch(url, options);
  
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    const responseData = await response.json();
    return responseData;
  }

  // File upload method - handles FormData
  async postFormData(endpoint, formData, authHeadersProvider = null) {
    const url = `${this.baseUrl}${endpoint}`;
    
    let authHeaders = {};
    
    if (authHeadersProvider) {
      authHeaders = await authHeadersProvider();
    }
    
    // Don't set Content-Type for FormData - let browser set it with boundary
    const headers = { 
      ...authHeaders
    };

    const options = {
      method: 'POST',
      headers,
      body: formData
    };

    const response = await fetch(url, options);
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    const responseData = await response.json();
    return responseData;
  }

  // File download method - handles binary data
  async downloadFile(endpoint, params = {}, authHeadersProvider = null) {
    const queryString = new URLSearchParams(params).toString();
    const url = `${this.baseUrl}${endpoint}${queryString ? `?${queryString}` : ''}`;
    
    let authHeaders = {};
    
    if (authHeadersProvider) {
      authHeaders = await authHeadersProvider();
    }
    
    const headers = { 
      ...authHeaders
    };

    const options = {
      method: 'GET',
      headers
    };

    const response = await fetch(url, options);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // Return the response as ArrayBuffer for binary data
    const arrayBuffer = await response.arrayBuffer();
    return arrayBuffer;
  }

  async postDownload(endpoint, body = {}, authHeadersProvider = null) {
    const url = `${this.baseUrl}${endpoint}`;
    
    let authHeaders = {};
    
    if (authHeadersProvider) {
      authHeaders = await authHeadersProvider();
    }
    
    const headers = { 
      ...authHeaders,
      'Content-Type': 'application/json'
    };

    const options = {
      method: 'POST',
      headers,
      body: JSON.stringify(body)
    };

    const response = await fetch(url, options);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // Return the response as ArrayBuffer for binary data
    const arrayBuffer = await response.arrayBuffer();
    return arrayBuffer;
  }

  async get(endpoint, params = {}, authHeadersProvider = null) {
    const queryString = new URLSearchParams(params).toString();
    const url = queryString ? `${endpoint}?${queryString}` : endpoint;
    return this.request(url, 'GET', null, {}, authHeadersProvider);
  }

  async post(endpoint, data, customHeaders = {}, authHeadersProvider = null) {
    return this.request(endpoint, 'POST', data, customHeaders, authHeadersProvider);
  }

  async put(endpoint, data, customHeaders = {}, authHeadersProvider = null) {
    return this.request(endpoint, 'PUT', data, customHeaders, authHeadersProvider);
  }

  async delete(endpoint, data = null, customHeaders = {}, authHeadersProvider = null) {
    return this.request(endpoint, 'DELETE', data, customHeaders, authHeadersProvider);
  }

  // Legacy methods - these will need authHeadersProvider passed in
  async getBAs(authHeadersProvider = null) {
    return this.get('/Lookups/GetBAs', {}, authHeadersProvider);
  }

  async saveInvoice(invoiceData, authHeadersProvider = null) {
    return this.post('/Invoices/SaveInvoice', invoiceData, {}, authHeadersProvider);
  }

  // File handling methods
  async getAttachments(headerId, authHeadersProvider = null) {
    return this.get('/Attachments/GetAttachments', { headerId }, authHeadersProvider);
  }

  async uploadAttachment(formData, authHeadersProvider = null) {
    return this.postFormData('/Attachments/UploadAttachment', formData, authHeadersProvider);
  }

  async downloadAttachment(attachmentId, authHeadersProvider = null) {
    return this.downloadFile('/Attachments/DownloadAttachment', { attachmentId }, authHeadersProvider);
  }

  async deleteAttachment(attachmentId, authHeadersProvider = null) {
    return this.delete('/Attachments/DeleteAttachment', { attachmentId }, {}, authHeadersProvider);
  }
}

export default new ApiService();