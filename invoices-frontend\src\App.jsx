import { AuthProvider } from './Auth/AuthProvider';
import { InvoiceProvider } from './components/InvoiceDashboard/InvoiceProvider';
import Layout from './components/LandingPage/Layout';
import { useEffect } from 'react';
import { Routes, Route, useLocation } from 'react-router-dom';
import InvoiceDashboard from './components/InvoiceDashboard';
import AdminPage from './pages/AdminPage';
import LogsPage from './pages/LogsPage';
import XRefPage from './pages/XRefPage';
import IntegrationStatusPage from './pages/IntegrationStatusPage';
import HelpPage from './pages/HelpPage';

// Updated with lowercase paths
const pageTitles = {
  '/': 'Dashboard - AP Automation',
  '/admin': 'Admin - AP Automation',
  '/logs': 'Logs - AP Automation',
  '/status': 'Integration Status - AP Automation',
  '/xref': 'Cross Reference - AP Automation',  // Lowercase
  '/help': 'Help Page'  // Lowercase
};

function App() {
  const location = useLocation();

  useEffect(() => {
    document.title = pageTitles[location.pathname] || 'Your App Name';
  }, [location.pathname]);

  return (
    <AuthProvider>
      <InvoiceProvider>
        <Layout>
          <Routes>
            <Route path="/" element={<InvoiceDashboard />} />
            <Route path="/admin" element={<AdminPage />} />
            <Route path="/logs" element={<LogsPage />} />
            <Route path="/status" element={<IntegrationStatusPage />} />
            <Route path="/xref" element={<XRefPage />} />  {/* Lowercase */}
            <Route path="/help" element={<HelpPage />} />  {/* Lowercase */}
          </Routes>
        </Layout>
      </InvoiceProvider>
    </AuthProvider>
  );
}

export default App;