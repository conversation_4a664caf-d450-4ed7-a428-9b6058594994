import { useContext } from 'react';
import InvoiceContext from './InvoiceContext'; // now default import
import { clearCacheFromStorage } from './InvoiceUtils';

export const useInvoiceContext = () => {
    const context = useContext(InvoiceContext);
    if (!context) {
        throw new Error('useInvoiceContext must be used within an InvoiceProvider');
    }
    return context;
};

export const useClearCacheOnDemand = () => {
    const { clearCache } = useInvoiceContext();
    return () => {
        clearCache();
        clearCacheFromStorage();
    };
};
