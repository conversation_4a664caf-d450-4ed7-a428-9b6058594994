import React, { useState, useEffect, useRef } from "react";
import { Dropdown } from "primereact/dropdown";
import { Calendar } from "primereact/calendar";
import { But<PERSON> } from "primereact/button";
import { useApi } from "../api/useApi";
import apiService from "../api/apiService";

export default function FilterBar({ filters, onChange, onApply, onReset }) {
  const [baOptions, setBaOptions] = useState([]);
  const [localError, setLocalError] = useState(null);
  const { loading, callApi } = useApi();

  // Ref to prevent multiple initial applies
  const hasAppliedInitialFilters = useRef(false);

  useEffect(() => {
    const fetchBAs = async () => {
      try {
        const data = await callApi(() => apiService.getBAs());
        setBaOptions(
          data.map((ba) => ({
            label: ba.value,
            value: ba.id,
          }))
        );
        setLocalError(null);
      } catch (err) {
        console.error("Failed to load BAs:", err);
        setLocalError(err.message);
        setBaOptions([]);
      }
    };

    fetchBAs();
  }, [callApi]);

  useEffect(() => {
    if (hasAppliedInitialFilters.current || loading) return;

    // Set default date ranges only if filters are not already set
    const now = new Date();

    // Clone dates safely for invoice dates
    const invoiceDateFrom = new Date(now);
    invoiceDateFrom.setMonth(invoiceDateFrom.getMonth() - 3);

    const invoiceDateTo = new Date(now);
    invoiceDateTo.setMonth(invoiceDateTo.getMonth() + 1);

    // Apply defaults only if not already set
    if (!filters.invoiceDateFrom) onChange("invoiceDateFrom", invoiceDateFrom);
    if (!filters.invoiceDateTo) onChange("invoiceDateTo", invoiceDateTo);
    
    // Set due dates to null instead of calculated ranges
    if (!filters.dueDateFrom) onChange("dueDateFrom", null);
    if (!filters.dueDateTo) onChange("dueDateTo", null);

    // Trigger apply once
    setTimeout(() => {
      onApply();
      hasAppliedInitialFilters.current = true;
    }, 0);
  }, [
    filters.counterparty,
    filters.invoiceDateFrom,
    filters.invoiceDateTo,
    filters.dueDateFrom,
    filters.dueDateTo,
    loading,
    onChange,
    onApply,
  ]);

  return (
    <div className="mb-3">
      <div className="p-4 card flex gap-4 align-items-end">
        <div className="flex flex-column">
          <label>Counterparty</label>
          <Dropdown
            value={filters.counterparty}
            options={baOptions}
            onChange={(e) => onChange("counterparty", e.value)}
            optionLabel="label"
            placeholder={
              loading
                ? "Loading..."
                : localError
                ? "Error loading options"
                : "Select Counterparty"
            }
            className="w-18rem"
            loading={loading}
            disabled={loading || !!localError}
            filter
            showClear
          />
          {localError && (
            <small className="p-error">Error: {localError}</small>
          )}
        </div>

        <div className="flex flex-column">
          <label>Invoice Date From</label>
          <Calendar
            value={filters.invoiceDateFrom}
            onChange={(e) => onChange("invoiceDateFrom", e.value)}
            dateFormat="yy-mm-dd"
            className="w-12rem"
            showIcon
          />
        </div>
        <div className="flex flex-column">
          <label>Invoice Date To</label>
          <Calendar
            value={filters.invoiceDateTo}
            onChange={(e) => onChange("invoiceDateTo", e.value)}
            dateFormat="yy-mm-dd"
            className="w-12rem"
            showIcon
          />
        </div>
        <div className="flex flex-column">
          <label>Due Date From</label>
          <Calendar
            value={filters.dueDateFrom}
            onChange={(e) => onChange("dueDateFrom", e.value)}
            dateFormat="yy-mm-dd"
            className="w-12rem"
            showIcon
          />
        </div>
        <div className="flex flex-column">
          <label>Due Date To</label>
          <Calendar
            value={filters.dueDateTo}
            onChange={(e) => onChange("dueDateTo", e.value)}
            dateFormat="yy-mm-dd"
            className="w-12rem"
            showIcon
          />
        </div>

        <div className="flex gap-2">
          <Button
            label="Reset"
            icon="pi pi-refresh"
            className="p-button-secondary"
            onClick={onReset}
            disabled={loading}
          />
          <Button
            label="Apply Filters"
            icon="pi pi-search"
            className="p-button-primary"
            onClick={onApply}
            disabled={loading}
          />
        </div>
      </div>
      <hr className="mt-0 mb-3 border-top-1 border-gray-300" />
    </div>
  );
}