@import "tailwindcss";
#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

/* App.css */
.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.top-menu {
  background: #333;
  color: white;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.auth-controls {
  display: flex;
  align-items: center;
}

.ms-login-button {
  background-color: #2F2F2F;
  color: white;
  border: none;
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 4px;
  cursor: pointer;
}

.ms-login-button:hover {
  background-color: #505050;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #4CAF50;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-weight: bold;
}

.content-container {
  display: flex;
  flex: 1;
}

.side-menu {
  width: 200px;
  background: #f0f0f0;
  padding: 1rem;
}

.main-content {
  flex: 1;
  padding: 1rem;
}

.welcome-message {
  text-align: center;
  margin-top: 3rem;
  padding: 2rem;
}

/* Add to your main CSS file */
.main-content {
  margin-left: 200px; /* Match this to the sidebar width */
  transition: margin-left 0.3s ease;
}

/* When sidebar is collapsed */
.side-menu-collapsed ~ .main-content {
  margin-left: 50px;
}

.toggle-checkbox:checked {
  right: 0;
  border-color: #4F46E5;
  transform: translateX(100%);
}

.toggle-checkbox:checked + .toggle-label {
  background-color: #4F46E5;
}

.toggle-checkbox {
  transition: all 0.3s ease-in-out;
  left: -0.25rem;
  top: -0.25rem;
}

/* App.css */
.status-toggle-container {
  display: flex;
  border: 1px solid #ced4da;
  border-radius: 6px;
  cursor: pointer;
  height: 2.5rem;
  overflow: hidden;
}

.status-toggle-option {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 1.5rem;
  min-width: 6rem;
  transition: all 0.3s ease;
}

.status-toggle-option.selected {
  background-color: #3b82f6;
  color: white;
}

.status-toggle-option:not(.selected) {
  background-color: white;
  color: #495057;
}