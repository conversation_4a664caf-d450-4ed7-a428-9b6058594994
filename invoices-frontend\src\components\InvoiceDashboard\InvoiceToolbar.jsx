import React from 'react';
import { exportInvoicesToCSV } from './CsvExport';

const getButtonConfig = (selectedRow) => {
    if (!selectedRow) return {};
    const { processingStatus: status } = selectedRow;
    switch (status) {
        case "Staged":
            return {
                edit: { disabled: false, label: "Edit" },
                process: { disabled: false, label: "Process" },
                override: { disabled: false, label: "Override" },
                ignore: { disabled: false, label: "Ignore" }
            };
        case "Processing":
            return {
                edit: { disabled: true, label: "Edit" },
                process: { disabled: false, label: "Cancel" },
                override: { disabled: true, label: "Override" },
                ignore: { disabled: true, label: "Ignore" }
            };
        case "Processed": case "Override":
             return {
                edit: { disabled: false, label: "Edit" },
                process: { disabled: false, label: "Reprocess" },
                override: { disabled: true, label: "Override" },
                ignore: { disabled: true, label: "Ignore" }
            };
        case "Ignored":
             return {
                edit: { disabled: true, label: "Edit" },
                process: { disabled: true, label: "Process" },
                override: { disabled: true, label: "Override" },
                ignore: { disabled: false, label: "UnIgnore" }
            };
        case "Completed":
            return {
                edit: { disabled: true, label: "Edit" },
                process: { disabled: true, label: "Process" },
                override: { disabled: true, label: "Override" },
                ignore: { disabled: true, label: "Ignore" }
            };
        default:
            return {};
    }
};

const ActionButton = ({ id, label, onClick, isLoading, disabled }) => {
    const isDisabled = disabled || isLoading;
    return (
        <button
            key={id} type="button" onClick={onClick} disabled={isDisabled}
            className="p-button p-button-raised"
            style={{
                border: '1px solid black', backgroundColor: 'white', color: 'black',
                opacity: isDisabled ? 0.5 : 1,
                pointerEvents: isDisabled ? 'none' : 'auto',
                cursor: isDisabled ? 'not-allowed' : 'pointer',
            }}
        >
            {isLoading ? <i className="pi pi-spin pi-spinner" style={{ fontSize: '1rem' }}></i> : label}
        </button>
    );
};

const StatusToggle = ({ currentStatus, onStatusChange }) => (
    <div className="status-toggle-container" style={{ display: 'flex', border: '2px solid #007bff', borderRadius: '8px', overflow: 'hidden' }}>
        {["Pending", "Processed"].map(statusValue => (
            <div
                key={statusValue}
                onClick={() => onStatusChange(statusValue)}
                style={{
                    padding: '10px 20px', cursor: 'pointer',
                    backgroundColor: currentStatus === statusValue ? '#007bff' : 'transparent',
                    color: currentStatus === statusValue ? 'white' : '#007bff',
                    fontWeight: currentStatus === statusValue ? 'bold' : 'normal',
                }}
            >
                {statusValue}
            </div>
        ))}
    </div>
);

const InvoiceToolbar = ({ 
    selectedId, 
    invoices, 
    details, // Add details prop
    actions, 
    loadingStates, 
    onStatusFilterChange, 
    statusFilter, 
    onEdit, 
    onNotes, 
    onLogs, 
    onPDF 
}) => {
    const selectedRow = invoices.find(h => h.headerID === selectedId);
    const buttonConfig = getButtonConfig(selectedRow);

    const handleExportClick = () => {
        if (invoices.length === 0) {
            alert('No data to export');
            return;
        }
        
        try {
            exportInvoicesToCSV(invoices, details);
        } catch (error) {
            console.error('Export failed:', error);
            alert('Export failed. Please try again.');
        }
    };

    const buttonList = [
        { id: "edit", label: buttonConfig.edit?.label || "Edit", onClick: onEdit, loading: loadingStates.edit, disabled: !selectedId || buttonConfig.edit?.disabled },
        { id: "process", label: buttonConfig.process?.label || "Process", onClick: actions.handleProcessClick, loading: loadingStates.buttonLoading.process, disabled: !selectedId || buttonConfig.process?.disabled },
        { id: "override", label: buttonConfig.override?.label || "Override", onClick: actions.handleOverrideClick, loading: loadingStates.buttonLoading.override, disabled: !selectedId || buttonConfig.override?.disabled },
        { id: "ignore", label: buttonConfig.ignore?.label || "Ignore", onClick: actions.handleIgnoreClick, loading: loadingStates.buttonLoading.ignore, disabled: !selectedId || buttonConfig.ignore?.disabled },
        { id: "notes", label: "Notes", onClick: onNotes, disabled: !selectedId },
        { id: "logs", label: "Logs", onClick: onLogs, disabled: !selectedId },
        { id: "pdf", label: "Files", onClick: onPDF, disabled: !selectedId },
    ];

    return (
        <div className="flex justify-content-between px-4">
            <div className="flex gap-2">
                {buttonList.map(btn => (
                    <ActionButton key={btn.id} {...btn} />
                ))}
            </div>
            
            <div className="flex gap-2 align-items-center">
                {/* Export button */}
                <ActionButton 
                    id="export" 
                    label="Export CSV" 
                    onClick={handleExportClick}
                    disabled={!invoices || invoices.length === 0}
                />
                
                {/* Status toggle */}
                <StatusToggle currentStatus={statusFilter} onStatusChange={onStatusFilterChange} />
            </div>
        </div>
    );
};

export default InvoiceToolbar;