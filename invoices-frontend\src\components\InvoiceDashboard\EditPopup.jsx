import React, { useState, useEffect, useCallback } from "react";
import { Dialog } from "primereact/dialog";
import { InputText } from "primereact/inputtext";
import { Dropdown } from "primereact/dropdown";
import { Calendar } from "primereact/calendar";
import { <PERSON><PERSON> } from "primereact/button";
import BolSearch from "./BOLSearch";
import { useApi } from "../api/useApi";
import apiService from "../api/apiService";
import { useAuth } from "../../Auth/useAuth";

export default function EditPopup({ visible, data, details = [], onHide, onSave }) {
  const [header, setHeader] = useState({});
  const [detailRows, setDetailRows] = useState([]);
  const [showMatchBol, setShowMatchBol] = useState(false);
  const [matchingRows, setMatchingRows] = useState(new Set()); // Track which rows are being matched
  
  // Dropdown options state
  const [termsOptions, setTermsOptions] = useState([]);
  const [statusOptions, setStatusOptions] = useState([]);
  const [counterpartyOptions, setCounterpartyOptions] = useState([]);
  const [transactionTypeOptions, setTransactionTypeOptions] = useState([]);
  const [templateOptions, setTemplateOptions] = useState([]);
  const [productOptions, setProductOptions] = useState([]);
  const [uomOptions, setUomOptions] = useState([]);
  const [localeOptions, setLocaleOptions] = useState([]);
  const { getAuthHeaders } = useAuth();
  const { loading, callApi } = useApi();

  useEffect(() => {
    if (visible && data) {
      setHeader(data);
      setDetailRows(details || []);
    }
  }, [visible, data, details]);

  // Memoize loadDropdownData to fix the useEffect dependency warning
  const loadDropdownData = useCallback(async () => {
    try {
      const [
        terms, 
        statuses, 
        counterparties, 
        transactionTypes, 
        templates,
        products, 
        uoms,
        locales
      ] = await Promise.all([
        callApi(() => apiService.get('/Lookups/GetPaymentTerms', getAuthHeaders)),
        callApi(() => apiService.get('/Lookups/GetStatuses')),
        callApi(() => apiService.get('/Lookups/GetBAs')),
        callApi(() => apiService.get('/Lookups/GetTransactionTypes')),
        callApi(() => apiService.get('/Lookups/GetTemplates', getAuthHeaders)),
        callApi(() => apiService.get('/Lookups/GetProducts')),
        callApi(() => apiService.get('/Lookups/GetUoMs')),
        callApi(() => apiService.get('/Lookups/GetLocales')),
      ]);

      // Transform and set options while preserving current selections
      setTermsOptions(transformDropdownData(terms));
      setStatusOptions(transformDropdownData(statuses));
      setCounterpartyOptions(transformDropdownData(counterparties));
      setTransactionTypeOptions(transformDropdownData(transactionTypes));
      setTemplateOptions(transformDropdownData(templates));
      setProductOptions(transformDropdownData(products));
      setUomOptions(transformDropdownData(uoms));
      setLocaleOptions(transformDropdownData(locales));

    } catch (error) {
      console.error('Error loading dropdown data:', error);
    }
  }, [callApi, getAuthHeaders]);

  useEffect(() => {
    if (visible) {
      loadDropdownData();
    }
  }, [visible, loadDropdownData]);

  const transformDropdownData = (data) => {
    return data?.map(item => ({
      label: item.value || item.name,
      value: item.id
    })) || [];
  };

  const updateHeader = (field, value) => {
    setHeader(prev => ({ ...prev, [field]: value }));
  };

  const updateDetail = (index, field, value) => {
    const updated = [...detailRows];
    updated[index] = { ...updated[index], [field]: value };
    
    // Remove match status when any field is edited (except isMatched itself)
    if (field !== 'isMatched') {
      updated[index].isMatched = false;
    }
    
    if (field === 'quantity' || field === 'amount') {
      const quantity = field === 'quantity' ? parseFloat(value) || 0 : parseFloat(updated[index].quantity) || 0;
      const amount = field === 'amount' ? parseFloat(value) || 0 : parseFloat(updated[index].amount) || 0;
      updated[index].rate = (amount / quantity).toFixed(2);
    }
    
    setDetailRows(updated);
  };

  const deleteDetail = (index) => {
    setDetailRows(prev => prev.filter((_, i) => i !== index));
  };

  const addDetailRow = () => {
    setDetailRows(prev => [
      ...prev,
      {
        bol: '',
        gravity: '',
        origin: '',
        destination: '',
        productId: productOptions[0]?.value || null,
        uoMId: uomOptions[0]?.value || null,
        quantity: '',
        rate: '',
        amount: '0.00',
        transactionDate: null,
        isMatched: false
      }
    ]);
  };

  // Helper function to map backend detail to frontend format
  const mapBackendDetailToFrontend = (backendDetail, originalRow) => {
  return {
    pybleDtlId: backendDetail.pybleDtlId,
    // Preserve original search fields
    bol: originalRow.bol || backendDetail.bol || '',
    quantity: backendDetail.quantity || originalRow.quantity || '',
    amount: backendDetail.totalAmount || originalRow.amount || '0.00',
    transactionDate: originalRow.transactionDate || (backendDetail.transactionDate ? new Date(backendDetail.transactionDate) : null),
    
    // Use backend data for other fields
    gravity: backendDetail.specificGravity || originalRow.gravity || '',
    originId: backendDetail.originLcleId || originalRow.originId,
    destinationId: backendDetail.destinationLcleId || originalRow.destinationId,
    productId: backendDetail.prdctId || originalRow.productId,
    uoMId: backendDetail.displayUomid || originalRow.uoMId,
    rate: backendDetail.rate || originalRow.rate || '',
    transactionTypeID: backendDetail.trnsctnTypId || originalRow.transactionTypeID,
    isMatched: true,
    accountDetailId: backendDetail.accountDetailId,
    movementDocumentId: backendDetail.movementDocumentId
  };
};

  // Updated Match API call function
  const handleMatch = async (index) => {
  try {
    setMatchingRows(prev => new Set([...prev, index]));
    
    const rowData = detailRows[index];
    
    // Check if the row has a pybleDtlId (existing record)
    const matchRequest = {
      bol: rowData.bol,
      quantity: parseFloat(rowData.quantity) || 0,
      totalAmount: parseFloat(rowData.amount) || 0,
      transactionDate: rowData.transactionDate
    };

    const matchResult = await callApi(() =>
      apiService.post('/Match/TryMatch', matchRequest, {}, getAuthHeaders)
    );
    
    if (matchResult.success && matchResult.isMatched) {
      // Update the row with the matched data, preserving original search fields
      const updated = [...detailRows];
      const matchedData = mapBackendDetailToFrontend(matchResult.updatedDetail, rowData); // Pass original row
      updated[index] = { ...updated[index], ...matchedData };
      setDetailRows(updated);
    } else {
      // Set to false if no match found
      const updated = [...detailRows];
      updated[index] = { ...updated[index], isMatched: false };
      setDetailRows(updated);
    }
    
  } catch {
    // Set to false on error
    const updated = [...detailRows];
    updated[index] = { ...updated[index], isMatched: false };
    setDetailRows(updated);
  } finally {
    setMatchingRows(prev => {
      const newSet = new Set(prev);
      newSet.delete(index);
      return newSet;
    });
  }
};

  const handleSave = async () => {
    try {
      const payload = {
        header: {
          ...header,
          externalCounterpartyId: header.externalCounterpartyId,
          internalCounterpartyId: header.internalCounterpartyId,
          paymentTermsId: header.paymentTermsId,
          statusId: header.statusId,
          templateId: header.templateId,
          invoiceNumber: header.invoiceNumber,
          invoiceDate: header.invoiceDate,
          dueDate: header.dueDate,
          totalAmount: header.totalAmount
        },
        details: detailRows.map(row => ({
          ...row,
          quantity: parseFloat(row.quantity) || 0,
          rate: parseFloat(row.rate) || 0,
          amount: parseFloat(row.amount) || 0
        }))
      };

      const result = await callApi(() => 
        apiService.post('/Invoices/SaveInvoice', payload, {}, getAuthHeaders)
      );

      console.log('Save result:', result); // Debug log

      // Call onSave with the result - this should trigger handleEditSave
      if (onSave) {
        await onSave(result); // Make sure onSave is awaited
      }
      
      // Only hide if save was successful (handleEditSave will handle the popup state)
      if (result.success) {
        onHide();
      }
    } catch (error) {
      console.error('Error saving data:', error);
      // Pass error result to parent
      if (onSave) {
        await onSave({ success: false, message: error.message });
      }
    }
  };

  const addBolToDetails = (bolRecord) => {
    const mappedRecord = {
      bol: bolRecord.BOL || bolRecord.bol || '',
      gravity: bolRecord.Gravity || bolRecord.gravity || '',
      origin: bolRecord.Origin || bolRecord.origin || '',
      destination: bolRecord.Destination || bolRecord.destination || '',
      product: bolRecord.Product || bolRecord.product || '',
      uoM: bolRecord.UoM || bolRecord.uoM || '',
      transactionType: bolRecord.TransactionType || bolRecord.transactionType || '',
      
      originId: bolRecord.originID || bolRecord.originId || null,
      destinationId: bolRecord.destinationID || bolRecord.destinationId || null,
      productId: bolRecord.productID || bolRecord.productId || null,
      uoMId: bolRecord.uoMID || bolRecord.uoMId || null,
      transactionTypeID: bolRecord.transactionTypeID || bolRecord.transactionTypeId || null,
      
      quantity: bolRecord.Quantity || bolRecord.quantity || '',
      rate: bolRecord.Rate || bolRecord.rate || '',
      amount: bolRecord.Amount || bolRecord.amount || '0.00',
      
      transactionDate: bolRecord.TransactionDate || bolRecord.transactionDate 
        ? new Date(bolRecord.TransactionDate || bolRecord.transactionDate) 
        : null,
        
      isMatched: false // New rows start as unmatched
    };
    
    setDetailRows(prev => [...prev, mappedRecord]);
  };

  // Helper function to get row style based on match status
  const getRowStyle = (row) => {
    const baseStyle = {
      display: 'flex', 
      gap: '1rem', 
      flexWrap: 'nowrap', 
      minWidth: '1150px',
      padding: '0.5rem',
      borderRadius: '4px',
      transition: 'background-color 0.3s ease'
    };
    
    if (row.isMatched) {
      return {
        ...baseStyle,
        backgroundColor: '#d4edda', // Light green background for matched rows
        border: '1px solid #c3e6cb'
      };
    }
    
    return baseStyle;
  };

  if (loading && !termsOptions.length) {
    return (
      <Dialog header="Edit Invoice" visible={visible} style={{ width: '70vw' }} onHide={onHide} modal>
        <div className="flex justify-content-center align-items-center" style={{ height: '200px' }}>
          <i className="pi pi-spinner pi-spin" style={{ fontSize: '2rem' }}></i>
          <span className="ml-2">Loading...</span>
        </div>
      </Dialog>
    );
  }

  return (
    <Dialog 
      header="Edit Invoice" 
      visible={visible} 
      style={{ width: '70vw' }} 
      onHide={onHide} 
      modal
    >
      <div style={{ overflowX: 'auto', whiteSpace: 'nowrap' }} className="mb-4 pb-2 border-bottom-1 surface-border">
        <div style={{ display: 'flex', gap: '1rem', minWidth: '1000px', paddingBottom: '1rem' }}>
          <div className="flex flex-column" style={{ flexShrink: 0, minWidth: '200px' }}>
            <label>External Counterparty</label>
            <Dropdown 
              value={header.externalCounterpartyId} 
              options={counterpartyOptions} 
              onChange={e => updateHeader("externalCounterpartyId", e.value)} 
              placeholder={header.externalCounterparty} 
            />
          </div>
          <div className="flex flex-column" style={{ flexShrink: 0, minWidth: '200px' }}>
            <label>Internal Counterparty</label>
            <Dropdown 
              value={header.internalCounterpartyId} 
              options={counterpartyOptions} 
              onChange={e => updateHeader("internalCounterpartyId", e.value)} 
              placeholder={header.internalCounterparty} 
            />
          </div>
          <div className="flex flex-column" style={{ flexShrink: 0, minWidth: '150px' }}>
            <label>Invoice Number</label>
            <InputText value={header.invoiceNumber || ''} onChange={e => updateHeader("invoiceNumber", e.target.value)} />
          </div>
          <div className="flex flex-column" style={{ flexShrink: 0, minWidth: '150px' }}>
            <label>Payment Terms</label>
            <Dropdown 
              value={header.paymentTermsID} 
              options={termsOptions} 
              onChange={e => updateHeader("paymentTermsID", e.value)} 
              placeholder={header.paymentTermsID} 
            />
          </div>
          <div className="flex flex-column" style={{ flexShrink: 0, minWidth: '150px' }}>
            <label>Template</label>
            <Dropdown 
                value={header.templateId} 
                options={templateOptions} 
                onChange={(e) => updateHeader('templateId', e.value)} 
                placeholder={header.template}
                style={{ minWidth: '150px' }} 
            />
          </div>
          <div className="flex flex-column" style={{ flexShrink: 0, minWidth: '150px' }}>
            <label>Total Amount</label>
            <InputText 
              value={header.totalAmount} 
              onChange={e => updateHeader("totalAmount", e.target.value)} 
              placeholder={header.totalAmount} 
            />
          </div>
          <div className="flex flex-column" style={{ flexShrink: 0, minWidth: '170px' }}>
            <label>Invoice Date</label>
            <Calendar 
              value={header.invoiceDate ? new Date(header.invoiceDate) : null} 
              onChange={e => updateHeader("invoiceDate", e.value)} 
              dateFormat="yy-mm-dd" 
              showIcon 
            />
          </div>
          <div className="flex flex-column" style={{ flexShrink: 0, minWidth: '170px' }}>
            <label>Due Date</label>
            <Calendar 
              value={header.dueDate ? new Date(header.dueDate) : null} 
              onChange={e => updateHeader("dueDate", e.value)} 
              dateFormat="yy-mm-dd" 
              showIcon 
            />
          </div>
          <div className="flex flex-column" style={{ flexShrink: 0, minWidth: '150px' }}>
            <label>Status</label>
            <Dropdown 
              value={header.statusID} 
              options={statusOptions} 
              onChange={e => updateHeader("statusID", e.value)} 
              placeholder={header.statusID} 
            />
          </div>
        </div>
      </div>

      <div className="mt-4" style={{ maxHeight: '300px', overflowY: 'auto' }}>
        <h5>Details</h5>
        <div className="flex flex-column gap-3">
          <div style={{ display: 'flex', gap: '1rem', fontWeight: 'bold', fontSize: '0.9rem', minWidth: '1150px' }}>
            <div style={{ minWidth: '120px' }}>BOL</div>
            <div style={{ minWidth: '100px' }}>Gravity</div>
            <div style={{ minWidth: '120px' }}>Origin</div>
            <div style={{ minWidth: '120px' }}>Destination</div>
            <div style={{ minWidth: '120px' }}>Product</div>
            <div style={{ minWidth: '100px' }}>UoM</div>
            <div style={{ minWidth: '100px' }}>Quantity</div>
            <div style={{ minWidth: '100px' }}>Rate</div>
            <div style={{ minWidth: '100px' }}>Amount</div>
            <div style={{ minWidth: '120px' }}>Transaction Type</div>
            <div style={{ minWidth: '150px' }}>Transaction Date</div>
            <div style={{ minWidth: '100px' }}>Actions</div>
          </div>
          {detailRows.map((row, index) => (
            <div key={index} style={getRowStyle(row)}>
              <InputText 
                placeholder={row.bol} 
                value={row.bol} 
                onChange={(e) => updateDetail(index, 'bol', e.target.value)} 
                style={{ minWidth: '120px' }} 
              />
              <InputText 
                placeholder={row.gravity}
                value={row.gravity} 
                onChange={(e) => updateDetail(index, 'gravity', e.target.value)} 
                style={{ minWidth: '100px' }} 
              />
              <Dropdown 
                placeholder={row.origin} 
                value={row.originId} 
                options={localeOptions}
                onChange={(e) => updateDetail(index, 'originId', e.value)} 
                style={{ minWidth: '120px' }} 
              />
              <Dropdown 
                placeholder={row.destination} 
                value={row.destinationId} 
                options={localeOptions}
                onChange={(e) => updateDetail(index, 'destinationId', e.value)} 
                style={{ minWidth: '120px' }} 
              />
              <Dropdown 
                value={row.productId} 
                options={productOptions} 
                onChange={(e) => updateDetail(index, 'productId', e.value)} 
                placeholder={row.product}
                style={{ minWidth: '120px' }} 
              />
              <Dropdown 
                value={row.uoMId} 
                options={uomOptions} 
                onChange={(e) => updateDetail(index, 'uoMId', e.value)} 
                placeholder={row.uoM}
                style={{ minWidth: '100px' }} 
              />
              <InputText 
                type="number" 
                placeholder={row.quantity}  
                value={row.quantity} 
                onChange={(e) => updateDetail(index, 'quantity', e.target.value)} 
                style={{ minWidth: '100px' }} 
              />
              <InputText 
                disabled
                placeholder={row.rate}  
                value={row.rate} 
                style={{ minWidth: '100px' }} 
              />
              <InputText 
                type="number" 
                placeholder={row.amount}
                value={row.amount}
                onChange={(e) => updateDetail(index, 'amount', e.target.value)}
                style={{ minWidth: '100px' }} 
              />
              <Dropdown 
                value={row.transactionTypeID} 
                options={transactionTypeOptions} 
                onChange={(e) => updateDetail(index, 'transactionTypeID', e.value)} 
                placeholder={row.transactionType}
                style={{ minWidth: '100px' }} 
              />
              <Calendar 
                value={row.transactionDate ? new Date(row.transactionDate) : null} 
                onChange={(e) => updateDetail(index, 'transactionDate', e.value)} 
                dateFormat="yy-mm-dd" 
                style={{ minWidth: '150px' }} 
              />
              <div style={{ display: 'flex', gap: '0.5rem', minWidth: '100px' }}>
                <Button 
                  icon="pi pi-check" 
                  className={`p-button-success p-button-sm ${row.isMatched ? 'p-button-outlined' : ''}`}
                  onClick={() => handleMatch(index)}
                  tooltip={row.isMatched ? "Matched" : "Match"}
                  loading={matchingRows.has(index)}
                  disabled={row.isMatched || matchingRows.has(index)}
                  style={{ minWidth: '40px' }}
                />
                <Button 
                  icon="pi pi-trash" 
                  className="p-button-danger p-button-sm" 
                  onClick={() => deleteDetail(index)}
                  tooltip="Delete row"
                  style={{ minWidth: '40px' }}
                />
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="flex justify-content-between gap-2 mt-3">
        <div className="flex gap-2">
          <Button 
            label="Add Row" 
            icon="pi pi-plus" 
            onClick={addDetailRow} 
          />
          <Button 
            label="Search BOL" 
            icon="pi pi-search" 
            onClick={() => setShowMatchBol(true)}
            className="p-button-info"
          />
        </div>
        <div className="flex gap-2">
          <Button label="Cancel" className="p-button-secondary" onClick={onHide} />
          <Button label="Save" onClick={handleSave} loading={loading} />
        </div>
      </div>

      <BolSearch
        visible={showMatchBol}
        onHide={() => setShowMatchBol(false)}
        onAddBol={addBolToDetails}
        counterpartyOptions={counterpartyOptions}
      />
    </Dialog>
  );
}