export const exportInvoicesToCSV = (headers, details) => {
    const flatData = [];
    
    headers.forEach(header => {
        const headerDetails = details[header.headerID] || [];
        
        if (headerDetails.length === 0) {
            // Header with no details - export header row with empty detail fields
            flatData.push({
                // Header fields
                externalCounterparty: header.externalCounterparty,
                internalCounterparty: header.internalCounterparty,
                template: header.template,
                invoiceNumber: header.invoiceNumber,
                invoiceDate: header.invoiceDate,
                dueDate: header.dueDate,
                paymentTerms: header.paymentTerms,
                totalAmount: header.totalAmount,
                status: header.status,
                processingStatus: header.processingStatus,
                // Empty detail fields
                bol: '',
                transactionType: '',
                gravity: '',
                origin: '',
                destination: '',
                product: '',
                uoM: '',
                quantity: '',
                rate: '',
                amount: '',
                transactionDate: '',
                isMatched: ''
            });
        } else {
            // Header with details - repeat header data for each detail
            headerDetails.forEach(detail => {
                flatData.push({
                    externalCounterparty: header.externalCounterparty,
                    internalCounterparty: header.internalCounterparty,
                    template: header.template,
                    invoiceNumber: header.invoiceNumber,
                    invoiceDate: header.invoiceDate,
                    dueDate: header.dueDate,
                    paymentTerms: header.paymentTerms,
                    totalAmount: header.totalAmount,
                    status: header.status,
                    processingStatus: header.processingStatus,
                    hasNotes: header.hasNotes,
                    // Detail fields
                    bol: detail.bol,
                    transactionType: detail.transactionType,
                    gravity: detail.gravity,
                    origin: detail.origin,
                    destination: detail.destination,
                    product: detail.product,
                    uoM: detail.uoM,
                    quantity: detail.quantity,
                    rate: detail.rate,
                    amount: detail.amount,
                    transactionDate: detail.transactionDate,
                    isMatched: detail.isMatched
                });
            });
        }
    });
    
    // Convert to CSV
    const csvContent = convertToCSV(flatData);
    
    // Create and trigger download
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    
    link.setAttribute('href', url);
    link.setAttribute('download', `invoice_export_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
};

const convertToCSV = (data) => {
    if (!data || data.length === 0) return '';
    
    const headers = Object.keys(data[0]);
    const csvHeaders = headers.join(',');
    
    const csvRows = data.map(row => 
        headers.map(header => {
            const value = row[header];
            // Handle values that might contain commas, quotes, or newlines
            if (value === null || value === undefined) return '';
            const stringValue = String(value);
            if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
                return `"${stringValue.replace(/"/g, '""')}"`;
            }
            return stringValue;
        }).join(',')
    );
    
    return [csvHeaders, ...csvRows].join('\n');
};