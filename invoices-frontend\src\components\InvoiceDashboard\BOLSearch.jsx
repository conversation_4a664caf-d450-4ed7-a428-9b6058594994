import React, { useState, useEffect, useCallback } from "react";
import { Dialog } from "primereact/dialog";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { InputText } from "primereact/inputtext";
import { Calendar } from "primereact/calendar";
import { But<PERSON> } from "primereact/button";
import { Card } from "primereact/card";
import { Toast } from "primereact/toast";
import "primereact/resources/themes/lara-light-indigo/theme.css";
import "primereact/resources/primereact.min.css";
import apiService from "../api/apiService";
import { useApi } from "../api/useApi";
import { useAuth } from "../../Auth/useAuth";

export default function BolSearch({ visible, onHide, onAddBol }) {
  const [searchResults, setSearchResults] = useState([]);
  const [searchCriteria, setSearchCriteria] = useState({
    bol: '',
    transactionDate: null
  });
  const [globalFilterValue, setGlobalFilterValue] = useState('');
  
  const { getAuthHeaders } = useAuth();
  const { loading: searchLoading, callApi: callSearchApi } = useApi();
  const toast = React.useRef(null);

  useEffect(() => {
    if (visible) {
      // Clear previous search when dialog opens
      setSearchResults([]);
      setSearchCriteria({ bol: '', transactionDate: null });
      setGlobalFilterValue('');
    }
  }, [visible]);

  const handleSearch = useCallback(async () => {
    // Changed from OR to AND - now requires BOTH conditions
    const shouldSearch = 
      (searchCriteria.bol && searchCriteria.bol.length >= 3) && 
      searchCriteria.transactionDate;

    if (!shouldSearch) {
      return;
    }

    try {
      const searchParams = new URLSearchParams();
      if (searchCriteria.bol) {
        searchParams.append('bol', searchCriteria.bol);
      }
      if (searchCriteria.transactionDate) {
        searchParams.append('transactionDate', searchCriteria.transactionDate.toISOString().split('T')[0]);
      }

      const response = await callSearchApi(() => 
        apiService.get(`/Invoices/SearchBOL?${searchParams.toString()}`, {}, getAuthHeaders)
      );
      
      setSearchResults(response || []);
      
    } catch (err) {
      console.error("Error searching BOL: ", err);
      toast.current?.show({
        severity: 'error', 
        summary: 'Error', 
        detail: 'Failed to search BOL records'
      });
      setSearchResults([]);
    }
  }, [searchCriteria.bol, searchCriteria.transactionDate, callSearchApi, getAuthHeaders]);

  // Live search effect with debouncing
  useEffect(() => {
    if (!visible) return;

    // Changed from OR to AND - now requires BOTH conditions
    const shouldSearch = 
      (searchCriteria.bol && searchCriteria.bol.length >= 3) && 
      searchCriteria.transactionDate;

    if (shouldSearch) {
      const debounceTimer = setTimeout(() => {
        handleSearch();
      }, 500); // 500ms delay to avoid too many API calls

      return () => clearTimeout(debounceTimer);
    } else {
      // Clear results if criteria don't meet minimum requirements
      setSearchResults([]);
    }
  }, [searchCriteria.bol, searchCriteria.transactionDate, visible, handleSearch]);

  const handleAddBol = (bolRecord) => {
    onAddBol(bolRecord);
    toast.current?.show({
      severity: 'success', 
      summary: 'Success', 
      detail: `BOL ${bolRecord.BOL || bolRecord.bol} added to invoice details`
    });
  };

  const clearSearch = () => {
    setSearchCriteria({ bol: '', transactionDate: null });
    setSearchResults([]);
    setGlobalFilterValue('');
  };

  const onGlobalFilterChange = (e) => {
    setGlobalFilterValue(e.target.value);
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    
    try {
      const date = new Date(dateString);
      return isNaN(date.getTime()) 
        ? 'Invalid Date' 
        : date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
          });
    } catch (e) {
      console.error('Error formatting date:', e);
      return 'Invalid Date';
    }
  };

  // Format currency values
  const formatCurrency = (value) => {
    if (value === null || value === undefined || value === '') return '$0.00';
    return new Intl.NumberFormat('en-US', { 
      style: 'currency', 
      currency: 'USD' 
    }).format(parseFloat(value) || 0);
  };

  // Template for action buttons
  const actionBodyTemplate = (rowData) => {
    return (
      <Button 
        icon="pi pi-plus" 
        label="Add"
        size="small"
        onClick={() => handleAddBol(rowData)}
        tooltip="Add this BOL to invoice details"
      />
    );
  };

  const renderSearchHeader = () => {
    return (
      <div className="flex flex-column gap-3">
        <div className="flex justify-content-between align-items-center">
          <h3 className="m-0">Search BOL Records</h3>
          <div className="flex gap-2">
            <Button
              type="button"
              icon="pi pi-times"
              label="Clear"
              outlined
              onClick={clearSearch}
            />
          </div>
        </div>
        
        <div className="grid">
          <div className="col-12 md:col-6">
            <div className="flex flex-column gap-2">
              <label htmlFor="bolSearch">BOL Number</label>
              <InputText
                id="bolSearch"
                value={searchCriteria.bol}
                onChange={(e) => setSearchCriteria(prev => ({ ...prev, bol: e.target.value }))}
                placeholder="Enter at least 3 characters and select a date to search..."
                className="w-full"
              />
            </div>
          </div>
          
          <div className="col-12 md:col-6">
            <div className="flex flex-column gap-2">
              <label htmlFor="dateSearch">Transaction Date</label>
              <Calendar
                id="dateSearch"
                value={searchCriteria.transactionDate}
                onChange={(e) => setSearchCriteria(prev => ({ ...prev, transactionDate: e.value }))}
                dateFormat="yy-mm-dd"
                placeholder="Select date..."
                showIcon
                className="w-full"
              />
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderResultsHeader = () => {
    return (
      <div className="flex justify-content-between align-items-center">
        <h4 className="m-0">Search Results ({searchResults.length})</h4>
        <span className="p-input-icon-left">
          <i className="pi pi-search" style={{paddingLeft: '0.5rem'}} />
          <InputText
            value={globalFilterValue}
            onChange={onGlobalFilterChange}
            placeholder="Filter results..."
            style={{ paddingLeft: '2.5rem' }}
          />
        </span>
      </div>
    );
  };

  return (
    <Dialog 
      header="BOL Search" 
      visible={visible} 
      style={{ width: '90vw', maxWidth: '1200px' }} 
      onHide={onHide} 
      modal
      maximizable
    >
      <Toast ref={toast} />
      
      <div className="flex flex-column gap-4">
        {/* Search Criteria Section */}
        <Card>
          {renderSearchHeader()}
        </Card>

        {/* Search Results Section */}
        {searchResults.length > 0 && (
          <Card>
            <DataTable
              value={searchResults}
              loading={searchLoading}
              header={renderResultsHeader()}
              globalFilter={globalFilterValue}
              globalFilterFields={['BOL', 'bol', 'Origin', 'origin', 'Destination', 'destination', 'Product', 'product', 'UoM', 'uoM']}
              emptyMessage="No BOL records found"
              paginator
              rows={10}
              rowsPerPageOptions={[5, 10, 25]}
              scrollable
              scrollHeight="400px"
              showGridlines
              stripedRows
              resizableColumns
              columnResizeMode="expand"
            >
              <Column 
                field="BOL"
                header="BOL"
                sortable
                style={{ minWidth: '120px' }}
                body={(rowData) => rowData.BOL || rowData.bol || ''}
              />
              <Column 
                field="Gravity"
                header="Gravity"
                sortable
                style={{ minWidth: '100px' }}
                body={(rowData) => rowData.Gravity || rowData.gravity || ''}
              />
              <Column 
                field="Origin"
                header="Origin"
                sortable
                style={{ minWidth: '120px' }}
                body={(rowData) => rowData.Origin || rowData.origin || ''}
              />
              <Column 
                field="Destination"
                header="Destination"
                sortable
                style={{ minWidth: '120px' }}
                body={(rowData) => rowData.Destination || rowData.destination || ''}
              />
              <Column 
                field="Product"
                header="Product"
                sortable
                style={{ minWidth: '120px' }}
                body={(rowData) => rowData.Product || rowData.product || ''}
              />
              <Column 
                field="UoM"
                header="UoM"
                sortable
                style={{ minWidth: '100px' }}
                body={(rowData) => rowData.UoM || rowData.uoM || ''}
              />
              <Column 
                field="Quantity"
                header="Quantity"
                sortable
                body={(rowData) => (rowData.Quantity || rowData.quantity || '0')}
                style={{ minWidth: '100px' }}
              />
              <Column 
                field="Rate"
                header="Rate"
                sortable
                body={(rowData) => formatCurrency(rowData.Rate || rowData.rate)}
                style={{ minWidth: '100px' }}
              />
              <Column 
                field="Amount"
                header="Amount"
                sortable
                body={(rowData) => formatCurrency(rowData.Amount || rowData.amount)}
                style={{ minWidth: '100px' }}
              />
              <Column 
                field="TransactionType"
                header="Transaction Type"
                sortable
                style={{ minWidth: '120px' }}
                body={(rowData) => rowData.TransactionType || rowData.transactionType || ''}
              />
              <Column 
                field="TransactionDate"
                header="Transaction Date"
                sortable
                body={(rowData) => formatDate(rowData.TransactionDate || rowData.transactionDate)}
                style={{ minWidth: '150px' }}
              />
              <Column
                header="Action"
                body={actionBodyTemplate}
                style={{ minWidth: '100px', textAlign: 'center' }}
              />
            </DataTable>
          </Card>
        )}
      </div>

      <div className="flex justify-content-end gap-2 mt-3">
        <Button 
          label="Close" 
          className="p-button-secondary" 
          onClick={onHide} 
        />
      </div>
    </Dialog>
  );
}