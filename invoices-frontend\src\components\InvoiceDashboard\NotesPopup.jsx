import React, { useState, useEffect, useCallback } from "react";
import { Dialog } from "primereact/dialog";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { InputTextarea } from "primereact/inputtextarea";
import { <PERSON><PERSON> } from "primereact/button";
import "primereact/resources/themes/lara-light-indigo/theme.css";
import "primereact/resources/primereact.min.css";
import apiService from "../api/apiService";
import { useApi } from "../api/useApi";
import { useAuth } from "../../Auth/useAuth";

const NotesPopup = ({ visible, onHide, headerID }) => {
  const [notes, setNotes] = useState([]);
  const [newNote, setNewNote] = useState("");
  const { getAuthHeaders } = useAuth();

  // Removed notesError and saveError since they are unused
  const { loading: notesLoading, callApi: callNotesApi } = useApi();
  const { loading: saveLoading, callApi: callSaveApi } = useApi();

  const fetchNotes = useCallback(async () => {
    if (!headerID) return;

    try {
      const response = await callNotesApi(() =>
        apiService.get("/Notes/GetNotes", { headerId: headerID }, getAuthHeaders)
      );
      setNotes(response || []);
    } catch (err) {
      console.error("Error fetching notes: ", err);
    }
  }, [headerID, callNotesApi, getAuthHeaders]);

  useEffect(() => {
    if (visible && headerID) {
      fetchNotes();
    }
  }, [visible, headerID, fetchNotes]); // Now safe to include fetchNotes

  const handleSaveNote = async () => {
    if (!newNote.trim()) return;

    try {
      const noteData = {
        pybleHdrId: headerID,
        note: newNote,
      };

      await callSaveApi(() =>
        apiService.post("/Notes/SaveNote", noteData, {}, getAuthHeaders)
      );

      setNewNote("");
      await fetchNotes(); // Refresh notes after saving
    } catch (err) {
      console.error("Error saving note: ", err);
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";

    try {
      const date = new Date(dateString);
      return isNaN(date.getTime())
        ? "Invalid Date"
        : date.toLocaleString("en-US", {
            year: "numeric",
            month: "short",
            day: "numeric",
            hour: "2-digit",
            minute: "2-digit",
          });
    } catch (e) {
      console.error("Error formatting date:", e);
      return "Invalid Date";
    }
  };

  return (
    <Dialog
      header="Notes"
      visible={visible}
      onHide={onHide}
      style={{ width: "50vw" }}
      breakpoints={{ "960px": "75vw", "641px": "90vw" }}
    >
      <div className="flex flex-column gap-3">
        <div className="flex flex-column gap-2">
          <label htmlFor="newNote">Add New Note</label>
          <InputTextarea
            id="newNote"
            value={newNote}
            onChange={(e) => setNewNote(e.target.value)}
            rows={3}
            autoResize
          />
          <div className="flex justify-content-end">
            <Button
              label="Save Note"
              icon="pi pi-save"
              onClick={handleSaveNote}
              loading={saveLoading}
              disabled={!newNote.trim()}
            />
          </div>
        </div>

        <div className="mt-3">
          <h3>Note History</h3>
          <DataTable
            value={notes}
            loading={notesLoading}
            scrollable
            scrollHeight="300px"
            emptyMessage={notesLoading ? "Loading notes..." : "No notes found"}
          >
            <Column
              field="createdDateTime"
              header="Date"
              body={(rowData) => formatDate(rowData.createdDateTime)}
            />
            <Column
              field="user"
              header="Author"
              body={(rowData) => rowData.user}
            />
            <Column
              field="note"
              header="Note"
              style={{ whiteSpace: "pre-wrap" }}
              body={(rowData) => rowData.note}
            />
          </DataTable>
        </div>
      </div>
    </Dialog>
  );
};

export default NotesPopup;