// authService.js - Updated to work with MSAL React
import { msalApp } from "./msalInstance";

export const authService = {
  // Get the active account - now accepts instance parameter
  getActiveAccount: (instance = null) => {
    const msalInstance = instance || msalApp;
    if (!msalInstance) {
      return null;
    }
    const account = msalInstance.getActiveAccount();
    return account;
  },

  // Get user info for API calls - now accepts instance parameter
  getUserInfo: (instance = null) => {
    const msalInstance = instance || msalApp;
    if (!msalInstance) {
      return null;
    }
    
    const account = msalInstance.getActiveAccount();
    
    if (!account) {
      return null;
    }

    const userInfo = {
      id: account.idTokenClaims?.oid,
      email: account.idTokenClaims?.email || account.username,
      name: account.name,
      tenantId: account.idTokenClaims?.tid,
      rawClaims: account.idTokenClaims
    };
    
    return userInfo;
  },

  // Get access token for API calls - now accepts instance parameter
  getAccessToken: async (instance = null, scopes = null) => {
    const msalInstance = instance || msalApp;
    if (!msalInstance) {
      return null;
    }
    
    try {
      const account = msalInstance.getActiveAccount();
      
      if (!account) {
        return null;
      }

      
      // Use provided scopes or fallback to default
      const tokenScopes = scopes || "User.Read";
      
      const tokenResponse = await msalInstance.acquireTokenSilent({
        account,
        scopes: tokenScopes
      });

      
      return tokenResponse.accessToken;
    } catch (error) {
      
      if (error.name === 'InteractionRequiredAuthError') { //
      }
      
      return null;
    }
  },

  // Get auth headers for API calls - now accepts instance and scopes parameters
  getAuthHeaders: async (instance = null, scopes = null) => {
    
    const msalInstance = instance || msalApp;
    if (!msalInstance) {
      return {/*empty*/};
    }

    const headers = {};
    
    const userInfo = authService.getUserInfo(msalInstance);
    
    const token = await authService.getAccessToken(msalInstance, scopes);
    
    if (userInfo) {
      headers['X-User-Id'] = userInfo.id;
      headers['X-User-Email'] = userInfo.email;
      headers['X-User-Name'] = userInfo.name;
    }

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    
    return headers;
  }
};