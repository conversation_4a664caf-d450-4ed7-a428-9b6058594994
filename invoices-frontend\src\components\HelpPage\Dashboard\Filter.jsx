import React from 'react';
import { Card } from 'primereact/card';
import { Panel } from 'primereact/panel';
import { Badge } from 'primereact/badge';
import { Message } from 'primereact/message';
import { Divider } from 'primereact/divider';

const FilterBarHelp = () => {
  const filterFields = [
    {
      title: 'Counterparty',
      icon: 'pi pi-users',
      type: 'Dropdown Selection',
      description: 'Filter invoices by business associate (BA) counterparty.',
      usage: 'Select from the dropdown list of available counterparties loaded from the system.',
      features: [
        'Auto-loading from BA API endpoint',
        'Searchable dropdown with filter capability',
        'Clear selection option available',
        'Shows "Loading..." during data fetch',
        'Error handling for failed data loads'
      ],
      behavior: 'Filters invoice records to show only those associated with the selected counterparty.',
      notes: 'If loading fails, the dropdown will show an error message and remain disabled until refresh.'
    },
    {
      title: 'Invoice Date From',
      icon: 'pi pi-calendar',
      type: 'Date Calendar',
      description: 'Set the earliest invoice date for filtering records.',
      usage: 'Click the calendar icon or field to open date picker and select start date.',
      features: [
        'Calendar popup with date picker',
        'Date format: YYYY-MM-DD',
        'Manual date entry supported',
        'Clear field option available'
      ],
      behavior: 'Shows invoices with invoice dates on or after the selected date.',
      defaultValue: '3 months before current date',
      notes: 'Default is automatically set to 3 months prior to current date on initial load.'
    },
    {
      title: 'Invoice Date To',
      icon: 'pi pi-calendar',
      type: 'Date Calendar',
      description: 'Set the latest invoice date for filtering records.',
      usage: 'Click the calendar icon or field to open date picker and select end date.',
      features: [
        'Calendar popup with date picker',
        'Date format: YYYY-MM-DD',
        'Manual date entry supported',
        'Clear field option available'
      ],
      behavior: 'Shows invoices with invoice dates on or before the selected date.',
      defaultValue: '1 month after current date',
      notes: 'Default is automatically set to 1 month after current date on initial load.'
    },
    {
      title: 'Due Date From',
      icon: 'pi pi-calendar',
      type: 'Date Calendar',
      description: 'Set the earliest due date for filtering records.',
      usage: 'Click the calendar icon or field to open date picker and select start date.',
      features: [
        'Calendar popup with date picker',
        'Date format: YYYY-MM-DD',
        'Manual date entry supported',
        'Clear field option available'
      ],
      behavior: 'Shows invoices with due dates on or after the selected date.',
      defaultValue: 'Not set (null)',
      notes: 'No default value is set - field starts empty to include all due dates.'
    },
    {
      title: 'Due Date To',
      icon: 'pi pi-calendar',
      type: 'Date Calendar',
      description: 'Set the latest due date for filtering records.',
      usage: 'Click the calendar icon or field to open date picker and select end date.',
      features: [
        'Calendar popup with date picker',
        'Date format: YYYY-MM-DD',
        'Manual date entry supported',
        'Clear field option available'
      ],
      behavior: 'Shows invoices with due dates on or before the selected date.',
      defaultValue: 'Not set (null)',
      notes: 'No default value is set - field starts empty to include all due dates.'
    }
  ];

  const actionButtons = [
    {
      title: 'Apply Filters',
      icon: 'pi pi-search',
      description: 'Execute the current filter settings to update the invoice list.',
      usage: 'Click to apply all current filter values and refresh the invoice data display.',
      behavior: 'Sends filter parameters to the system and reloads invoice records matching criteria.',
      availability: 'Disabled during loading operations',
      notes: 'Automatically triggered on initial page load with default date ranges.'
    },
    {
      title: 'Reset',
      icon: 'pi pi-refresh',
      description: 'Clear all filter settings and return to default state.',
      usage: 'Click to clear all filter fields and reset to system defaults.',
      behavior: 'Clears counterparty selection, resets date ranges to defaults, and applies filters.',
      availability: 'Disabled during loading operations',
      notes: 'Resets invoice dates to default 3-month range but clears due date filters completely.'
    }
  ];

  const filterBehavior = [
    {
      scenario: 'Initial Load',
      description: 'System automatically sets default invoice date range (3 months back to 1 month forward) and applies filters.',
      result: 'Invoice list shows records within the default date range with no counterparty or due date restrictions.'
    },
    {
      scenario: 'Counterparty Only',
      description: 'Select counterparty but leave date fields with defaults.',
      result: 'Shows all invoices for selected counterparty within the default invoice date range.'
    },
    {
      scenario: 'Date Range Only',
      description: 'Modify date ranges but leave counterparty unselected.',
      result: 'Shows invoices from all counterparties within specified date ranges.'
    },
    {
      scenario: 'Combined Filters',
      description: 'Set counterparty and customize date ranges.',
      result: 'Shows invoices that match both counterparty selection and all specified date criteria.'
    },
    {
      scenario: 'Due Dates Only',
      description: 'Set due date range but leave invoice dates as default.',
      result: 'Shows invoices with due dates in specified range and invoice dates within default range.'
    }
  ];

  const headerTemplate = (
    <div className="bg-primary text-primary-50 p-4 border-round">
      <h1 className="text-3xl font-bold m-0 mb-2">Filter Bar - User Manual</h1>
      <p className="text-lg m-0 text-primary-100">
        Guide to filtering invoice records using counterparty and date range criteria.
      </p>
    </div>
  );

  return (
    <div className="flex flex-column gap-4">
      {/* Header Section */}
      <Card className="shadow-2">
        {headerTemplate}
      </Card>

      {/* Filter Overview */}
      <Card title="Filter Overview" className="shadow-2">
        <Message 
          severity="info" 
          text="The filter bar allows you to narrow down the invoice list by counterparty and date ranges. Filters work together - records must match ALL selected criteria to appear in results."
          className="mb-4"
        />
        
        <p className="text-700 mb-4">
          The system automatically applies default date ranges on initial load to show recent invoice activity. 
          You can customize these filters to focus on specific counterparties, time periods, or due date ranges.
        </p>

        <div className="grid">
          <div className="col-12 md:col-4">
            <div className="surface-100 border-round p-4 h-full border-left-3 border-blue-500">
              <div className="flex align-items-center mb-3">
                <i className="pi pi-filter text-lg text-blue-500 mr-2"></i>
                <h4 className="m-0">Filter Logic</h4>
              </div>
              <p className="text-600 text-sm m-0">
                All filter criteria are combined with AND logic. Records must satisfy every selected filter to appear in the results.
              </p>
            </div>
          </div>

          <div className="col-12 md:col-4">
            <div className="surface-100 border-round p-4 h-full border-left-3 border-green-500">
              <div className="flex align-items-center mb-3">
                <i className="pi pi-clock text-lg text-green-500 mr-2"></i>
                <h4 className="m-0">Auto-Apply</h4>
              </div>
              <p className="text-600 text-sm m-0">
                Filters are automatically applied on page load with default date ranges. Manual apply is required for subsequent changes.
              </p>
            </div>
          </div>

          <div className="col-12 md:col-4">
            <div className="surface-100 border-round p-4 h-full border-left-3 border-orange-500">
              <div className="flex align-items-center mb-3">
                <i className="pi pi-exclamation-triangle text-lg text-orange-500 mr-2"></i>
                <h4 className="m-0">Date Ranges</h4>
              </div>
              <p className="text-600 text-sm m-0">
                Invoice dates have automatic defaults, while due dates start empty to include all records unless specifically filtered.
              </p>
            </div>
          </div>
        </div>
      </Card>

      {/* Filter Fields Detail */}
      <Card title="Filter Fields" className="shadow-2">
        <p className="text-600 mb-4">
          Detailed information about each filter field and its behavior:
        </p>
        
        <div className="grid">
          {filterFields.map((field, index) => (
            <div key={index} className="col-12">
              <Panel 
                header={
                  <div className="flex align-items-center">
                    <i className={`${field.icon} text-primary mr-2`}></i>
                    <span className="font-semibold">{field.title}</span>
                    <Badge value={field.type} severity="secondary" className="ml-2" />
                  </div>
                } 
                className="mb-3"
                toggleable
              >
                <div className="grid">
                  <div className="col-12 md:col-8">
                    <p className="text-700 mb-3">{field.description}</p>
                    
                    <div className="mb-3">
                      <h5 className="text-primary mb-2">Usage:</h5>
                      <p className="text-600 text-sm m-0">{field.usage}</p>
                    </div>

                    <div className="mb-3">
                      <h5 className="text-primary mb-2">Filter Behavior:</h5>
                      <p className="text-600 text-sm m-0">{field.behavior}</p>
                    </div>

                    {field.defaultValue && (
                      <div className="mb-3">
                        <h5 className="text-primary mb-2">Default Value:</h5>
                        <Badge value={field.defaultValue} severity="info" />
                      </div>
                    )}
                  </div>
                  
                  <div className="col-12 md:col-4">
                    <div className="surface-100 border-round p-3">
                      <h5 className="text-primary mb-2">Features</h5>
                      <ul className="text-600 text-sm pl-3 m-0">
                        {field.features.map((feature, featureIndex) => (
                          <li key={featureIndex}>{feature}</li>
                        ))}
                      </ul>
                    </div>
                    
                    {field.notes && (
                      <div className="surface-50 border-round p-2 mt-3">
                        <p className="text-600 text-sm m-0"><strong>Note:</strong> {field.notes}</p>
                      </div>
                    )}
                  </div>
                </div>
              </Panel>
            </div>
          ))}
        </div>
      </Card>

      {/* Action Buttons */}
      <Card title="Filter Actions" className="shadow-2">
        <p className="text-600 mb-4">
          Control buttons for applying and managing filter settings:
        </p>
        
        <div className="grid">
          {actionButtons.map((button, index) => (
            <div key={index} className="col-12 md:col-6">
              <div className="surface-50 border-round p-4 h-full border-left-3 border-primary">
                <div className="flex align-items-center mb-3">
                  <i className={`${button.icon} text-lg text-primary mr-2`}></i>
                  <h4 className="m-0">{button.title}</h4>
                </div>
                
                <p className="text-600 text-sm mb-3">{button.description}</p>
                
                <div className="mb-3">
                  <h5 className="text-primary text-sm mb-2">Usage:</h5>
                  <p className="text-600 text-sm m-0">{button.usage}</p>
                </div>

                <div className="mb-3">
                  <h5 className="text-primary text-sm mb-2">Behavior:</h5>
                  <p className="text-600 text-sm m-0">{button.behavior}</p>
                </div>

                <div className="mb-3">
                  <h5 className="text-primary text-sm mb-2">Availability:</h5>
                  <p className="text-600 text-sm m-0">{button.availability}</p>
                </div>

                <div className="surface-100 border-round p-2 mt-3">
                  <p className="text-600 text-sm m-0"><strong>Note:</strong> {button.notes}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Filter Scenarios */}
      <Card title="Common Filter Scenarios" className="shadow-2">
        <p className="text-600 mb-4">
          Examples of how different filter combinations work and their expected results:
        </p>
        
        <div className="grid">
          {filterBehavior.map((scenario, index) => (
            <div key={index} className="col-12">
              <div className="surface-100 border-round p-4 mb-3 border-left-3 border-indigo-500">
                <div className="flex align-items-center mb-2">
                  <Badge value={`Scenario ${index + 1}`} severity="secondary" className="mr-2" />
                  <h4 className="m-0">{scenario.scenario}</h4>
                </div>
                
                <div className="grid">
                  <div className="col-12 md:col-6">
                    <h5 className="text-indigo-600 text-sm mb-2">Description:</h5>
                    <p className="text-600 text-sm m-0">{scenario.description}</p>
                  </div>
                  <div className="col-12 md:col-6">
                    <h5 className="text-indigo-600 text-sm mb-2">Expected Result:</h5>
                    <p className="text-600 text-sm m-0">{scenario.result}</p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Best Practices */}
      <Card title="Filter Best Practices" className="shadow-2">
        <div className="grid">
          <div className="col-12 md:col-6">
            <h4 className="text-green-600 mb-3">Recommendations</h4>
            <ul className="text-600 text-sm pl-3 m-0">
              <li>Use default date ranges as starting point for most searches</li>
              <li>Select specific counterparty when looking for particular vendor invoices</li>
              <li>Set due date filters when managing payment schedules</li>
              <li>Apply filters after making multiple changes to avoid unnecessary API calls</li>
              <li>Clear counterparty filter to see invoices from all vendors</li>
              <li>Use Reset button to quickly return to default settings</li>
            </ul>
          </div>
          <div className="col-12 md:col-6">
            <h4 className="text-orange-600 mb-3">Performance Tips</h4>
            <ul className="text-600 text-sm pl-3 m-0">
              <li>Narrow date ranges for faster query performance on large datasets</li>
              <li>Wait for counterparty dropdown to load before making selections</li>
              <li>Avoid very wide date ranges unless necessary</li>
              <li>Use specific counterparty filters to reduce result set size</li>
              <li>Apply filters rather than constantly changing criteria</li>
              <li>Reset filters if system appears to be loading slowly</li>
            </ul>
          </div>
        </div>
        
        <Divider />
        
        <div className="grid">
          <div className="col-12 md:col-6">
            <Message 
              severity="info" 
              text="Default invoice date range (3 months back to 1 month forward) covers most recent invoice activity efficiently."
            />
          </div>
          <div className="col-12 md:col-6">
            <Message 
              severity="warn" 
              text="Due date filters start empty to include all records - set only when specifically needed for payment planning."
            />
          </div>
        </div>
      </Card>
    </div>
  );
};

export default FilterBarHelp;