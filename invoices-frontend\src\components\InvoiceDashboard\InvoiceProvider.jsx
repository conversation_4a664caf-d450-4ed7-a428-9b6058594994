import React, { useReducer } from 'react';
import InvoiceContext from './InvoiceContext';
import { loadPersistedState, invoiceReducer, getInitialFilters } from './InvoiceUtils';

export const InvoiceProvider = ({ children }) => {
    const [state, dispatch] = useReducer(invoiceReducer, null, loadPersistedState);

    const isDataStale = () => {
        if (!state.lastFetchTime) return true;
        const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
        return state.lastFetchTime < fiveMinutesAgo;
    };

    const value = {
        ...state,
        isDataStale: isDataStale(),
        dispatch,
        updateFilter: (field, value) => dispatch({ type: 'UPDATE_FILTER', field, value }),
        setStatusFilter: (status) => dispatch({ type: 'SET_STATUS_FILTER', payload: status }),
        setSelectedId: (id) => dispatch({ type: 'SET_SELECTED_ID', payload: id }),
        setLoading: (loading) => dispatch({ type: 'SET_LOADING', payload: loading }),
        setData: (data) => dispatch({ type: 'SET_DATA', payload: data }),
        resetFilters: () => dispatch({ type: 'RESET_FILTERS' }),
        resetFiltersAndFetch: (fetchCallback) => {
            dispatch({ type: 'RESET_FILTERS' });
            setTimeout(() => {
                const resetFilters = getInitialFilters();
                fetchCallback(resetFilters);
            }, 0);
        },
        clearCache: () => dispatch({ type: 'CLEAR_CACHE' }),
        setFiltersInitialized: (initialized) =>
            dispatch({ type: 'SET_FILTERS_INITIALIZED', payload: initialized }),
    };

    return (
        <InvoiceContext.Provider value={value}>
            {children}
        </InvoiceContext.Provider>
    );
};
