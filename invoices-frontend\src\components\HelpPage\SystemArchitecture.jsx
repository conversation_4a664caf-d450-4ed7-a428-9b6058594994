import React, { useState } from 'react';
import { Card } from 'primereact/card';

const SystemArchitectureDiagram = () => {
  const [hoveredComponent, setHoveredComponent] = useState(null);

  const components = [
    // Input Layer
    { id: 'email', name: 'Email Inbox', x: 50, y: 50, layer: 'input', icon: 'pi-envelope', color: 'bg-blue-500' },
    
    // Processing Layer
    { id: 'ocr', name: 'Microsoft Forms OCR', x: 250, y: 50, layer: 'processing', icon: 'pi-eye', color: 'bg-purple-500' },
    { id: 'databricks', name: 'Databricks Storage', x: 450, y: 50, layer: 'processing', icon: 'pi-database', color: 'bg-orange-500' },
    { id: 'dotnet', name: '.NET Backend', x: 650, y: 50, layer: 'processing', icon: 'pi-cog', color: 'bg-green-500' },
    
    // Database Layer
    { id: 'raw', name: 'Raw Table', x: 150, y: 200, layer: 'database', icon: 'pi-table', color: 'bg-gray-500' },
    { id: 'matching', name: 'Matching Process', x: 350, y: 200, layer: 'database', icon: 'pi-sync', color: 'bg-indigo-500' },
    { id: 'staging', name: 'Staging Table', x: 550, y: 200, layer: 'database', icon: 'pi-table', color: 'bg-gray-600' },
    
    // Application Layer
    { id: 'frontend', name: 'React Frontend', x: 250, y: 350, layer: 'application', icon: 'pi-desktop', color: 'bg-cyan-500' },
    { id: 'backend', name: '.NET Backend API', x: 450, y: 350, layer: 'application', icon: 'pi-server', color: 'bg-green-600' },
    
    // Integration Layer
    { id: 'integration', name: 'Integration Table', x: 350, y: 500, layer: 'integration', icon: 'pi-table', color: 'bg-gray-700' },
    { id: 'rightangle-task', name: 'RightAngle Task', x: 150, y: 650, layer: 'rightangle', icon: 'pi-play', color: 'bg-red-500' },
    { id: 'rightangle-db', name: 'RightAngle DB', x: 350, y: 650, layer: 'rightangle', icon: 'pi-database', color: 'bg-red-600' },
    { id: 'rightangle-s23', name: 'RightAngle S23', x: 550, y: 650, layer: 'rightangle', icon: 'pi-sitemap', color: 'bg-red-700' }
  ];

  const connections = [
    { from: 'email', to: 'ocr' },
    { from: 'ocr', to: 'databricks' },
    { from: 'databricks', to: 'dotnet' },
    { from: 'dotnet', to: 'raw' },
    { from: 'raw', to: 'matching' },
    { from: 'matching', to: 'staging' },
    { from: 'staging', to: 'frontend' },
    { from: 'staging', to: 'backend' },
    { from: 'frontend', to: 'backend' },
    { from: 'backend', to: 'integration' },
    { from: 'integration', to: 'rightangle-task' },
    { from: 'rightangle-task', to: 'rightangle-db' },
    { from: 'rightangle-db', to: 'rightangle-s23' }
  ];

  const getComponentById = (id) => components.find(c => c.id === id);

  const renderConnection = (connection, index) => {
    const fromComp = getComponentById(connection.from);
    const toComp = getComponentById(connection.to);
    
    if (!fromComp || !toComp) return null;

    const fromX = fromComp.x + 60; // Center of component
    const fromY = fromComp.y + 30;
    const toX = toComp.x + 60;
    const toY = toComp.y + 30;

    // Calculate arrow position
    const angle = Math.atan2(toY - fromY, toX - fromX);
    const arrowLength = 10;
    const arrowAngle = Math.PI / 6;

    const arrowX1 = toX - arrowLength * Math.cos(angle - arrowAngle);
    const arrowY1 = toY - arrowLength * Math.sin(angle - arrowAngle);
    const arrowX2 = toX - arrowLength * Math.cos(angle + arrowAngle);
    const arrowY2 = toY - arrowLength * Math.sin(angle + arrowAngle);

    return (
      <g key={index}>
        <line
          x1={fromX}
          y1={fromY}
          x2={toX}
          y2={toY}
          stroke="#64748b"
          strokeWidth="2"
          markerEnd="url(#arrowhead)"
        />
        {/* Arrow head */}
        <polygon
          points={`${toX},${toY} ${arrowX1},${arrowY1} ${arrowX2},${arrowY2}`}
          fill="#64748b"
        />
      </g>
    );
  };

  const componentDescriptions = {
    email: 'Receives invoice emails with PDF/image attachments',
    ocr: 'Extracts data using optical character recognition',
    databricks: 'Stores raw extracted data for processing',
    dotnet: 'Integrates with RightAngle staging database',
    raw: 'Initial storage of invoice data in RightAngle',
    matching: 'Matches vendors and accounts automatically',
    staging: 'Validated data ready for user review',
    frontend: 'User interface for reviewing and approving invoices',
    backend: 'API layer handling business logic and data operations',
    integration: 'Final processed data ready for RightAngle',
    'rightangle-task': 'Executes invoice processing workflows',
    'rightangle-db': 'Core RightAngle database system',
    'rightangle-s23': 'RightAngle application layer'
  };

  return (
    <Card title="System Architecture" className="shadow-2">
      <div className="mb-4">
        <p className="text-600">
          Interactive system architecture showing the complete invoice processing flow. 
          Hover over components to see detailed descriptions.
        </p>
      </div>
      
      <div className="relative overflow-auto" style={{ minHeight: '750px' }}>
        <svg width="800" height="750" className="w-full">
          {/* Define arrow marker */}
          <defs>
            <marker
              id="arrowhead"
              markerWidth="10"
              markerHeight="7"
              refX="9"
              refY="3.5"
              orient="auto"
            >
              <polygon points="0 0, 10 3.5, 0 7" fill="#64748b" />
            </marker>
          </defs>

          {/* Render connections */}
          {connections.map((connection, index) => renderConnection(connection, index))}

          {/* Render components */}
          {components.map((component) => (
            <g key={component.id}>
              <rect
                x={component.x}
                y={component.y}
                width="120"
                height="60"
                rx="8"
                className={`${component.color} cursor-pointer transition-all duration-200 ${
                  hoveredComponent === component.id ? 'opacity-90 stroke-2 stroke-white' : 'opacity-80'
                }`}
                onMouseEnter={() => setHoveredComponent(component.id)}
                onMouseLeave={() => setHoveredComponent(null)}
              />
              <text
                x={component.x + 60}
                y={component.y + 25}
                textAnchor="middle"
                className="fill-white text-xs font-semibold pointer-events-none"
              >
                <tspan x={component.x + 60} dy="0">{component.name.split(' ')[0]}</tspan>
                <tspan x={component.x + 60} dy="12">{component.name.split(' ').slice(1).join(' ')}</tspan>
              </text>
              <text
                x={component.x + 60}
                y={component.y + 50}
                textAnchor="middle"
                className={`pi ${component.icon} fill-white text-sm pointer-events-none`}
              />
            </g>
          ))}
        </svg>

        {/* Layer Labels */}
        <div className="absolute left-0 top-0 flex flex-column gap-4 p-2">
          <div className="text-xs font-semibold text-gray-600 transform -rotate-90 origin-center" 
               style={{ marginTop: '50px' }}>INPUT</div>
          <div className="text-xs font-semibold text-gray-600 transform -rotate-90 origin-center" 
               style={{ marginTop: '80px' }}>PROCESSING</div>
          <div className="text-xs font-semibold text-gray-600 transform -rotate-90 origin-center" 
               style={{ marginTop: '80px' }}>DATABASE</div>
          <div className="text-xs font-semibold text-gray-600 transform -rotate-90 origin-center" 
               style={{ marginTop: '80px' }}>APPLICATION</div>
          <div className="text-xs font-semibold text-gray-600 transform -rotate-90 origin-center" 
               style={{ marginTop: '80px' }}>INTEGRATION</div>
          <div className="text-xs font-semibold text-gray-600 transform -rotate-90 origin-center" 
               style={{ marginTop: '80px' }}>RIGHTANGLE</div>
        </div>
      </div>

      {/* Component Description */}
      {hoveredComponent && (
        <div className="mt-4 p-3 bg-blue-50 border-round">
          <div className="flex align-items-center mb-2">
            <i className={`pi ${getComponentById(hoveredComponent)?.icon} text-blue-600 mr-2`}></i>
            <h4 className="m-0 text-blue-800">{getComponentById(hoveredComponent)?.name}</h4>
          </div>
          <p className="text-blue-700 m-0 text-sm">
            {componentDescriptions[hoveredComponent]}
          </p>
        </div>
      )}

      {/* Legend */}
      <div className="mt-4 grid">
        <div className="col-12 md:col-6">
          <h5 className="text-primary mb-3">Data Flow Legend</h5>
          <div className="flex align-items-center mb-2">
            <div className="w-4 h-1 bg-gray-500 mr-2"></div>
            <span className="text-sm text-600">Data Flow Direction</span>
          </div>
          <div className="flex align-items-center mb-2">
            <div className="w-4 h-4 bg-blue-500 border-round mr-2"></div>
            <span className="text-sm text-600">Input Sources</span>
          </div>
          <div className="flex align-items-center mb-2">
            <div className="w-4 h-4 bg-purple-500 border-round mr-2"></div>
            <span className="text-sm text-600">Processing Components</span>
          </div>
        </div>
        <div className="col-12 md:col-6">
          <h5 className="text-primary mb-3">System Layers</h5>
          <div className="flex align-items-center mb-2">
            <div className="w-4 h-4 bg-gray-600 border-round mr-2"></div>
            <span className="text-sm text-600">Database Tables</span>
          </div>
          <div className="flex align-items-center mb-2">
            <div className="w-4 h-4 bg-cyan-500 border-round mr-2"></div>
            <span className="text-sm text-600">Application Layer</span>
          </div>
          <div className="flex align-items-center mb-2">
            <div className="w-4 h-4 bg-red-600 border-round mr-2"></div>
            <span className="text-sm text-600">RightAngle Systems</span>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default SystemArchitectureDiagram;