import React from 'react';
import { Card } from 'primereact/card';
import { Panel } from 'primereact/panel';
import { Badge } from 'primereact/badge';
import { Message } from 'primereact/message';
import { Divider } from 'primereact/divider';
import { Button } from 'primereact/button';

const EditScreenHelp = () => {
  const headerFields = [
    {
      field: 'External Counterparty',
      description: 'Vendor or external entity issuing the invoice.',
      dataType: 'Dropdown',
      required: true,
      notes: 'Select from validated counterparty list loaded from system lookups.'
    },
    {
      field: 'Internal Counterparty',
      description: 'Internal company entity receiving the invoice.',
      dataType: 'Dropdown',
      required: true,
      notes: 'Select from internal counterparty list. Determines processing rules.'
    },
    {
      field: 'Invoice Number',
      description: 'Unique identifier for the invoice document.',
      dataType: 'Text Input',
      required: true,
      notes: 'Must be unique per vendor. System checks for duplicates.'
    },
    {
      field: 'Payment Terms',
      description: 'Payment schedule and terms for the invoice.',
      dataType: 'Dropdown',
      required: false,
      notes: 'Loaded from payment terms lookup. Affects due date calculation.'
    },
    {
      field: 'Template',
      description: 'Invoice template for processing configuration.',
      dataType: 'Dropdown',
      required: false,
      notes: 'Determines processing workflow and validation rules.'
    },
    {
      field: 'Total Amount',
      description: 'Total invoice amount including all charges.',
      dataType: 'Number Input',
      required: true,
      notes: 'Should match sum of detail line amounts. System validates totals.'
    },
    {
      field: 'Invoice Date',
      description: 'Date the invoice was issued by the vendor.',
      dataType: 'Date Picker',
      required: true,
      notes: 'Format: YYYY-MM-DD. Affects aging and due date calculations.'
    },
    {
      field: 'Due Date',
      description: 'Date payment is due to the vendor.',
      dataType: 'Date Picker',
      required: false,
      notes: 'Auto-calculated based on invoice date and payment terms if blank.'
    },
    {
      field: 'Status',
      description: 'Current processing status of the invoice.',
      dataType: 'Dropdown',
      required: true,
      notes: 'Controls available actions. Cannot be changed to certain statuses manually.'
    }
  ];

  const detailFields = [
    {
      field: 'BOL',
      description: 'Bill of Lading number for shipment reference.',
      dataType: 'Text Input',
      usage: 'Used for matching against movement data. Enter exactly as shown on documents.',
      matchingBehavior: 'Primary matching field - system searches for exact BOL matches.'
    },
    {
      field: 'Gravity',
      description: 'Specific gravity measurement for the product.',
      dataType: 'Number Input',
      usage: 'Decimal value representing product density. Auto-populated from matching.',
      matchingBehavior: 'Updated from matched movement data when BOL match is found.'
    },
    {
      field: 'Origin',
      description: 'Location where the product originated.',
      dataType: 'Dropdown',
      usage: 'Select from locale lookup list. Required for proper accounting.',
      matchingBehavior: 'Populated from movement data during matching process.'
    },
    {
      field: 'Destination',
      description: 'Location where the product was delivered.',
      dataType: 'Dropdown',
      usage: 'Select from locale lookup list. Must match delivery documentation.',
      matchingBehavior: 'Populated from movement data during matching process.'
    },
    {
      field: 'Product',
      description: 'Type of product being invoiced.',
      dataType: 'Dropdown',
      usage: 'Select from product master list. Affects pricing and accounting.',
      matchingBehavior: 'Updated from movement data when successful match occurs.'
    },
    {
      field: 'UoM',
      description: 'Unit of measure for quantity calculations.',
      dataType: 'Dropdown',
      usage: 'Must align with quantity field. Common values: BBL, GAL, MT.',
      matchingBehavior: 'Set from movement data to ensure unit consistency.'
    },
    {
      field: 'Quantity',
      description: 'Amount of product being invoiced.',
      dataType: 'Number Input',
      usage: 'Used for rate calculation. Triggers automatic rate computation when changed.',
      matchingBehavior: 'Primary matching field - compared against movement quantities.'
    },
    {
      field: 'Rate',
      description: 'Price per unit (auto-calculated).',
      dataType: 'Display Only',
      usage: 'Automatically calculated as Amount ÷ Quantity. Cannot be edited directly.',
      matchingBehavior: 'Recalculated when quantity or amount changes.'
    },
    {
      field: 'Amount',
      description: 'Total line item amount.',
      dataType: 'Number Input',
      usage: 'Line total for this item. Triggers rate recalculation when changed.',
      matchingBehavior: 'Primary matching field - compared against expected amounts.'
    },
    {
      field: 'Transaction Type',
      description: 'Type of transaction for accounting purposes.',
      dataType: 'Dropdown',
      usage: 'Select appropriate transaction classification. Affects GL coding.',
      matchingBehavior: 'Updated from movement data during matching process.'
    },
    {
      field: 'Transaction Date',
      description: 'Date the transaction occurred.',
      dataType: 'Date Picker',
      usage: 'Format: YYYY-MM-DD. Should match movement or delivery date.',
      matchingBehavior: 'Primary matching field - used to find corresponding movements.'
    }
  ];

  const matchingStates = [
    {
      state: 'Unmatched',
      description: 'Row has not been matched against system data.',
      appearance: 'Normal background color',
      actions: 'Match button available and enabled',
      behavior: 'All fields editable. Match button shows "Match" text.'
    },
    {
      state: 'Matched',
      description: 'Row successfully matched with movement data.',
      appearance: 'Light green background with border',
      actions: 'Match button disabled and shows checkmark',
      behavior: 'Fields populated from matched data. Editing removes match status.'
    },
    {
      state: 'Matching',
      description: 'Match operation currently in progress.',
      appearance: 'Normal background with loading indicator',
      actions: 'Match button shows spinner and is disabled',
      behavior: 'All controls disabled until match operation completes.'
    }
  ];

  const actions = [
    {
      action: 'Match',
      icon: 'pi pi-check',
      description: 'Attempt to match detail row against system movement data.',
      usage: 'Click to search for matching BOL, quantity, amount, and transaction date.',
      behavior: [
        'Sends BOL, quantity, amount, and transaction date to matching API',
        'If match found, populates all other fields with matched data',
        'Row background changes to green and match button becomes disabled',
        'If no match found, row remains unchanged with match button available'
      ],
      requirements: ['BOL field must contain a value', 'Row must not already be matched']
    },
    {
      action: 'Delete Row',
      icon: 'pi pi-trash',
      description: 'Remove the detail row from the invoice.',
      usage: 'Click to permanently delete the row. Cannot be undone.',
      behavior: [
        'Immediately removes row from detail list',
        'No confirmation dialog - action is immediate',
        'Changes are not saved until Save button is clicked'
      ],
      requirements: ['No special requirements - always available']
    },
    {
      action: 'Add Row',
      icon: 'pi pi-plus',
      description: 'Add a new blank detail row to the invoice.',
      usage: 'Click to append a new row with default values.',
      behavior: [
        'Creates new row at bottom of detail list',
        'Product and UoM set to first available option',
        'All other fields blank and ready for input',
        'Amount defaults to 0.00'
      ],
      requirements: ['No special requirements - always available']
    },
    {
      action: 'Search BOL',
      icon: 'pi pi-search',
      description: 'Open BOL search dialog to find and add BOL records.',
      usage: 'Click to search system BOL database and add selected records.',
      behavior: [
        'Opens BOL search popup with filtering options',
        'Selected BOL records are added as new detail rows',
        'Added rows start as unmatched and require matching'
      ],
      requirements: ['No special requirements - always available']
    }
  ];

  const headerTemplate = (
    <div className="bg-primary text-primary-50 p-4 border-round">
      <h1 className="text-3xl font-bold m-0 mb-2">Edit Invoice Screen - User Manual</h1>
      <p className="text-lg m-0 text-primary-100">
        Complete guide to editing invoice headers and detail records
      </p>
    </div>
  );

  return (
    <div className="flex flex-column gap-4">
      {/* Header Section */}
      <Card className="shadow-2">
        {headerTemplate}
      </Card>

      {/* Screen Overview */}
      <Card title="Screen Overview" className="shadow-2">
        <Message 
          severity="info" 
          text="The Edit Invoice screen allows modification of invoice header information and detail line items. Use this screen to correct OCR extraction errors and validate invoice data before processing."
          className="mb-4"
        />
        
        <div className="grid">
          <div className="col-12 md:col-4">
            <Panel header="Header Section" className="h-full">
              <p className="text-600 text-sm mb-3">
                Contains invoice-level information including vendor, dates, amounts, and processing settings.
              </p>
              <ul className="text-600 text-sm pl-3 m-0">
                <li>Counterparty information</li>
                <li>Invoice identification</li>
                <li>Payment terms and dates</li>
                <li>Processing template and status</li>
              </ul>
            </Panel>
          </div>

          <div className="col-12 md:col-4">
            <Panel header="Detail Section" className="h-full">
              <p className="text-600 text-sm mb-3">
                Contains line item details for products, quantities, and amounts. Supports matching against movement data.
              </p>
              <ul className="text-600 text-sm pl-3 m-0">
                <li>BOL and shipment information</li>
                <li>Product and location details</li>
                <li>Quantity and pricing data</li>
                <li>Transaction classification</li>
              </ul>
            </Panel>
          </div>

          <div className="col-12 md:col-4">
            <Panel header="Action Buttons" className="h-full">
              <p className="text-600 text-sm mb-3">
                Provides functions to manage detail rows, search for BOL records, and save changes.
              </p>
              <ul className="text-600 text-sm pl-3 m-0">
                <li>Match detail rows with system data</li>
                <li>Add and remove detail rows</li>
                <li>Search and import BOL records</li>
                <li>Save or cancel changes</li>
              </ul>
            </Panel>
          </div>
        </div>
      </Card>

      {/* Header Fields */}
      <Card title="Header Fields Reference" className="shadow-2">
        <p className="text-600 mb-4">
          Invoice header fields control overall invoice processing and must be accurately completed:
        </p>
        
        <div className="grid">
          {headerFields.map((field, index) => (
            <div key={index} className="col-12">
              <div className="surface-50 border-round p-3 mb-3 border-left-3 border-blue-500">
                <div className="flex align-items-center justify-content-between mb-2">
                  <h5 className="m-0 text-primary">{field.field}</h5>
                  <div className="flex gap-2">
                    <Badge value={field.dataType} severity="info" />
                    {field.required && <Badge value="Required" severity="danger" />}
                  </div>
                </div>
                <p className="text-600 text-sm mb-2">{field.description}</p>
                <div className="surface-100 border-round p-2">
                  <p className="text-600 text-sm m-0"><strong>Notes:</strong> {field.notes}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Detail Fields */}
      <Card title="Detail Fields Reference" className="shadow-2">
        <p className="text-600 mb-4">
          Detail row fields represent individual line items and support automatic matching with system movement data:
        </p>
        
        <div className="grid">
          {detailFields.map((field, index) => (
            <div key={index} className="col-12 lg:col-6">
              <div className="surface-50 border-round p-3 mb-3 border-left-3 border-green-500">
                <div className="flex align-items-center justify-content-between mb-2">
                  <h5 className="m-0 text-primary">{field.field}</h5>
                  <Badge value={field.dataType} severity="secondary" />
                </div>
                <p className="text-600 text-sm mb-2">{field.description}</p>
                
                <div className="mb-2">
                  <h6 className="text-sm font-semibold m-0 mb-1">Usage:</h6>
                  <p className="text-600 text-sm m-0">{field.usage}</p>
                </div>
                
                <div className="surface-100 border-round p-2">
                  <h6 className="text-sm font-semibold m-0 mb-1">Matching Behavior:</h6>
                  <p className="text-600 text-sm m-0">{field.matchingBehavior}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Matching System */}
      <Card title="Row Matching System" className="shadow-2">
        <Message 
          severity="warn" 
          text="The matching system attempts to find corresponding movement records based on BOL, quantity, amount, and transaction date. Matched data populates remaining fields automatically."
          className="mb-4"
        />
        
        <h4 className="text-primary mb-3">Matching States</h4>
        <div className="grid mb-4">
          {matchingStates.map((state, index) => (
            <div key={index} className="col-12 md:col-4">
              <div className={`border-round p-3 mb-3 ${state.state === 'Matched' ? 'bg-green-50 border-green-200' : 'surface-100'}`}>
                <div className="flex align-items-center mb-2">
                  <Badge 
                    value={state.state} 
                    severity={state.state === 'Matched' ? 'success' : state.state === 'Matching' ? 'warning' : 'secondary'} 
                  />
                </div>
                <p className="text-600 text-sm mb-2">{state.description}</p>
                
                <div className="mb-2">
                  <h6 className="text-sm font-semibold m-0 mb-1">Appearance:</h6>
                  <p className="text-600 text-sm m-0">{state.appearance}</p>
                </div>
                
                <div className="mb-2">
                  <h6 className="text-sm font-semibold m-0 mb-1">Available Actions:</h6>
                  <p className="text-600 text-sm m-0">{state.actions}</p>
                </div>
                
                <div>
                  <h6 className="text-sm font-semibold m-0 mb-1">Behavior:</h6>
                  <p className="text-600 text-sm m-0">{state.behavior}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        <h4 className="text-primary mb-3">Important Matching Notes</h4>
        <ul className="text-600 text-sm pl-3 m-0">
          <li>Editing any field (except isMatched) removes the matched status from the row</li>
          <li>Rate is automatically calculated as Amount ÷ Quantity when either field changes</li>
          <li>Matching preserves original search fields (BOL, quantity, amount, transaction date)</li>
          <li>Backend data populates other fields when match is successful</li>
          <li>Failed matches leave the row unchanged and available for retry</li>
        </ul>
      </Card>

      {/* Available Actions */}
      <Card title="Available Actions" className="shadow-2">
        <p className="text-600 mb-4">
          The edit screen provides several actions for managing invoice data and detail rows:
        </p>
        
        <div className="grid">
          {actions.map((action, index) => (
            <div key={index} className="col-12">
              <Panel 
                header={
                  <div className="flex align-items-center">
                    <i className={`${action.icon} text-primary mr-2`}></i>
                    <span className="font-semibold">{action.action}</span>
                  </div>
                } 
                className="mb-3"
                toggleable
              >
                <div className="grid">
                  <div className="col-12 md:col-8">
                    <p className="text-700 mb-3">{action.description}</p>
                    
                    <div className="mb-3">
                      <h5 className="text-primary mb-2">Usage:</h5>
                      <p className="text-600 text-sm m-0">{action.usage}</p>
                    </div>

                    <div className="mb-3">
                      <h5 className="text-primary mb-2">Requirements:</h5>
                      <ul className="text-600 text-sm pl-3 m-0">
                        {action.requirements.map((req, reqIndex) => (
                          <li key={reqIndex}>{req}</li>
                        ))}
                      </ul>
                    </div>

                    <div className="surface-100 border-round p-3">
                      <h5 className="text-primary mb-2">Behavior</h5>
                      <ul className="text-600 text-sm pl-3 m-0">
                        {action.behavior.map((behavior, behaviorIndex) => (
                          <li key={behaviorIndex}>{behavior}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                  
                  <div className="col-12 md:col-4">
                    <div className="surface-50 border-round p-3 text-center">
                      <h6 className="text-primary mb-3">Button Examples</h6>
                      <div className="flex flex-column gap-3 align-items-center">
                        {action.action === 'Match' && (
                          <>
                            <Button 
                              icon="pi pi-check" 
                              className="p-button-success p-button-sm" 
                              tooltip="Unmatched - Click to match"
                              disabled={false}
                            />
                            <span className="text-sm text-600">Unmatched State</span>
                            
                            <Button 
                              icon="pi pi-spin pi-spinner" 
                              className="p-button-success p-button-sm" 
                              loading={true}
                              disabled={true}
                            />
                            <span className="text-sm text-600">Matching in Progress</span>
                            
                            <Button 
                              icon="pi pi-check" 
                              className="p-button-success p-button-sm p-button-outlined" 
                              tooltip="Matched successfully"
                              disabled={true}
                            />
                            <span className="text-sm text-600">Matched State</span>
                          </>
                        )}
                        
                        {action.action === 'Delete Row' && (
                          <>
                            <Button 
                              icon="pi pi-trash" 
                              className="p-button-danger p-button-sm" 
                              tooltip="Delete this row"
                            />
                            <span className="text-sm text-600">Delete Button</span>
                          </>
                        )}
                        
                        {action.action === 'Add Row' && (
                          <>
                            <Button 
                              label="Add Row" 
                              icon="pi pi-plus" 
                              className="p-button-sm"
                            />
                            <span className="text-sm text-600">Add Row Button</span>
                          </>
                        )}
                        
                        {action.action === 'Search BOL' && (
                          <>
                            <Button 
                              label="Search BOL" 
                              icon="pi pi-search" 
                              className="p-button-info p-button-sm"
                            />
                            <span className="text-sm text-600">Search BOL Button</span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </Panel>
            </div>
          ))}
        </div>
      </Card>

      {/* Best Practices */}
      <Card title="Best Practices" className="shadow-2">
        <div className="grid">
          <div className="col-12 md:col-6">
            <h4 className="text-green-600 mb-3">Recommended Workflow</h4>
            <ol className="text-600 text-sm pl-3 m-0">
              <li>Review and correct header information first</li>
              <li>Verify counterparty selections are accurate</li>
              <li>Check invoice number for duplicates</li>
              <li>Validate total amount matches detail sum</li>
              <li>Attempt to match all detail rows with system data</li>
              <li>Review matched data for accuracy</li>
              <li>Manually correct any mismatched information</li>
              <li>Save changes and verify processing status</li>
            </ol>
          </div>
          <div className="col-12 md:col-6">
            <h4 className="text-orange-600 mb-3">Common Issues</h4>
            <ul className="text-600 text-sm pl-3 m-0">
              <li>BOL numbers with extra spaces or formatting</li>
              <li>Quantity/amount mismatches preventing matching</li>
              <li>Incorrect transaction dates affecting match results</li>
              <li>Missing or incorrect counterparty selections</li>
              <li>Duplicate invoice numbers causing save failures</li>
              <li>Total amount not matching sum of detail amounts</li>
            </ul>
          </div>
        </div>
        
        <Divider />
        
        <Message 
          severity="success" 
          text="Always attempt to match detail rows before saving. Matched rows have higher processing confidence and reduce manual review requirements."
        />
      </Card>
    </div>
  );
};

export default EditScreenHelp;