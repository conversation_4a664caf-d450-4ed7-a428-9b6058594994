import React from 'react';
import { Card } from 'primereact/card';
import { Panel } from 'primereact/panel';
import { Badge } from 'primereact/badge';
import { Message } from 'primereact/message';
import { Divider } from 'primereact/divider';
import { Button } from 'primereact/button';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';

const LogsHelp = () => {
  const logFields = [
    {
      field: 'logDate',
      header: 'Date',
      type: 'DateTime',
      description: 'Timestamp when the log entry was created.',
      filterType: 'Date Filter',
      sortable: true,
      example: 'Jan 15, 2025, 2:30 PM'
    },
    {
      field: 'user',
      header: 'User',
      type: 'Text',
      description: 'Username or identifier of the person who performed the action.',
      filterType: 'Text Search',
      sortable: true,
      example: '<EMAIL>'
    },
    {
      field: 'invoiceNumber',
      header: 'Invoice Number',
      type: 'Text',
      description: 'Associated invoice number if the log entry relates to invoice processing.',
      filterType: 'Text Search',
      sortable: true,
      example: 'INV-2025-001234'
    },
    {
      field: 'message',
      header: 'Message',
      type: 'Text (Multi-line)',
      description: 'Detailed description of the action or event that was logged.',
      filterType: 'Text Search',
      sortable: true,
      example: 'Invoice processed successfully. Total amount: $1,250.00'
    }
  ];

  const actions = [
    {
      action: 'Search Logs',
      icon: 'pi pi-search',
      description: 'Search across all log fields for specific information.',
      usage: 'Type in the global search box to filter logs by any field content.',
      behavior: [
        'Searches across user, message, and invoice number fields',
        'Updates results in real-time as you type',
        'Case-insensitive partial matching',
        'Combines with column-specific filters'
      ],
      requirements: ['No special requirements']
    },
    {
      action: 'Filter by Date',
      icon: 'pi pi-calendar',
      description: 'Filter logs to show entries from a specific date.',
      usage: 'Click the calendar icon in the Date column header to select a date.',
      behavior: [
        'Opens calendar picker for date selection',
        'Shows only logs from the selected date',
        'Can be combined with other filters',
        'Clear filter to show all dates'
      ],
      requirements: ['No special requirements']
    },
    {
      action: 'Filter by Column',
      icon: 'pi pi-filter',
      description: 'Apply specific filters to individual columns.',
      usage: 'Use filter inputs in column headers to narrow down results.',
      behavior: [
        'Each column has its own filter input',
        'Filters work independently and can be combined',
        'Text filters support partial matching',
        'Date filter requires exact date match'
      ],
      requirements: ['No special requirements']
    },
    {
      action: 'Sort Columns',
      icon: 'pi pi-sort',
      description: 'Sort logs by any column in ascending or descending order.',
      usage: 'Click column headers to sort. Click again to reverse order.',
      behavior: [
        'Single-click sorts ascending',
        'Double-click sorts descending',
        'Supports multi-column sorting',
        'Sort indicator shows current order'
      ],
      requirements: ['No special requirements']
    },
    {
      action: 'Clear Filters',
      icon: 'pi pi-filter-slash',
      description: 'Remove all applied filters and show all log entries.',
      usage: 'Click the "Clear" button to reset all filters.',
      behavior: [
        'Clears global search filter',
        'Removes all column-specific filters',
        'Resets date filters',
        'Shows complete log dataset'
      ],
      requirements: ['No special requirements']
    },
    {
      action: 'Refresh Data',
      icon: 'pi pi-refresh',
      description: 'Reload log data from the server to get latest entries.',
      usage: 'Click the "Refresh" button to fetch current log data.',
      behavior: [
        'Fetches latest logs from database',
        'Shows loading indicator during refresh',
        'Maintains current filters after refresh',
        'Updates table with new entries'
      ],
      requirements: ['No special requirements']
    },
    {
      action: 'Pagination',
      icon: 'pi pi-chevron-left',
      description: 'Navigate through pages of log entries.',
      usage: 'Use pagination controls at bottom to navigate between pages.',
      behavior: [
        'Default shows 25 logs per page',
        'Can change to 10, 25, 50, or 100 per page',
        'Shows current page and total records',
        'Navigate with first, previous, next, last buttons'
      ],
      requirements: ['No special requirements']
    }
  ];

  const useCases = [
    {
      title: 'Troubleshooting Issues',
      description: 'Find error messages and trace problem resolution.',
      steps: [
        'Search for specific error keywords in the global search',
        'Filter by date range when the issue occurred',
        'Sort by date to see chronological order of events',
        'Review message details for error context'
      ]
    },
    {
      title: 'User Activity Monitoring',
      description: 'Track user actions and system interactions.',
      steps: [
        'Filter by specific user in the User column',
        'Review chronological activity by sorting by date',
        'Search for specific actions or operations',
        'Monitor patterns of user behavior'
      ]
    },
    {
      title: 'Invoice Processing Audit',
      description: 'Review invoice-related system activities.',
      steps: [
        'Filter by specific invoice number',
        'Search for "invoice" in global search',
        'Sort by date to see processing timeline',
        'Review success/failure messages'
      ]
    },
    {
      title: 'System Performance Review',
      description: 'Analyze system operations and performance patterns.',
      steps: [
        'Filter by date range for analysis period',
        'Search for performance-related keywords',
        'Review frequency of different log types',
        'Identify patterns in system behavior'
      ]
    }
  ];

  const headerTemplate = (
    <div className="bg-primary text-primary-50 p-4 border-round">
      <h1 className="text-3xl font-bold m-0 mb-2">System Logs - User Manual</h1>
      <p className="text-lg m-0 text-primary-100">
        Complete guide to viewing and analyzing system activity logs
      </p>
    </div>
  );

  return (
    <div className="flex flex-column gap-4">
      {/* Header Section */}
      <Card className="shadow-2">
        {headerTemplate}
      </Card>

      {/* Screen Overview */}
      <Card title="System Overview" className="shadow-2">
        <Message 
          severity="info" 
          text="The System Logs page provides a comprehensive view of all system activities, user actions, and processing events. Use this interface to monitor system health, troubleshoot issues, and audit user activities."
          className="mb-4"
        />
        
        <div className="grid">
          <div className="col-12 md:col-4">
            <Panel header="Activity Monitoring" className="h-full">
              <p className="text-600 text-sm mb-3">
                Track all system activities and user interactions in real-time.
              </p>
              <ul className="text-600 text-sm pl-3 m-0">
                <li>User login/logout events</li>
                <li>Data processing activities</li>
                <li>System operations</li>
                <li>Error and warning messages</li>
              </ul>
            </Panel>
          </div>

          <div className="col-12 md:col-4">
            <Panel header="Search & Filter" className="h-full">
              <p className="text-600 text-sm mb-3">
                Powerful filtering capabilities to find specific log entries quickly.
              </p>
              <ul className="text-600 text-sm pl-3 m-0">
                <li>Global text search</li>
                <li>Date-based filtering</li>
                <li>Column-specific filters</li>
                <li>Multi-column sorting</li>
              </ul>
            </Panel>
          </div>

          <div className="col-12 md:col-4">
            <Panel header="Data Management" className="h-full">
              <p className="text-600 text-sm mb-3">
                Efficient data display with pagination and customizable views.
              </p>
              <ul className="text-600 text-sm pl-3 m-0">
                <li>Paginated results</li>
                <li>Adjustable page sizes</li>
                <li>Resizable columns</li>
                <li>Real-time data refresh</li>
              </ul>
            </Panel>
          </div>
        </div>
      </Card>

      {/* Log Fields Reference */}
      <Card title="Log Fields Reference" className="shadow-2">
        <p className="text-600 mb-4">
          Understanding the information contained in each log entry:
        </p>
        
        <DataTable 
          value={logFields} 
          className="mb-4"
          showGridlines
          stripedRows
        >
          <Column 
            field="header" 
            header="Field" 
            style={{ width: '15%' }}
            body={(rowData) => <Badge value={rowData.header} severity="info" />}
          />
          <Column 
            field="type" 
            header="Data Type" 
            style={{ width: '15%' }}
          />
          <Column 
            field="description" 
            header="Description" 
            style={{ width: '35%' }}
          />
          <Column 
            field="filterType" 
            header="Filter Type" 
            style={{ width: '15%' }}
          />
          <Column 
            field="example" 
            header="Example" 
            style={{ width: '20%' }}
            body={(rowData) => (
              <div className="font-mono text-sm bg-gray-100 p-1 border-round">
                {rowData.example}
              </div>
            )}
          />
        </DataTable>
      </Card>

      {/* Available Actions */}
      <Card title="Available Actions" className="shadow-2">
        <p className="text-600 mb-4">
          The logs page provides various tools for searching, filtering, and managing log data:
        </p>
        
        <div className="grid">
          {actions.map((action, index) => (
            <div key={index} className="col-12 md:col-6">
              <Panel 
                header={
                  <div className="flex align-items-center">
                    <i className={`${action.icon} text-primary mr-2`}></i>
                    <span className="font-semibold">{action.action}</span>
                  </div>
                } 
                className="mb-3 h-full"
                toggleable
              >
                <p className="text-700 mb-3">{action.description}</p>
                
                <div className="mb-3">
                  <h6 className="text-primary mb-2">Usage:</h6>
                  <p className="text-600 text-sm m-0">{action.usage}</p>
                </div>

                <div className="surface-100 border-round p-3">
                  <h6 className="text-primary mb-2">Behavior</h6>
                  <ul className="text-600 text-sm pl-3 m-0">
                    {action.behavior.map((behavior, behaviorIndex) => (
                      <li key={behaviorIndex}>{behavior}</li>
                    ))}
                  </ul>
                </div>
              </Panel>
            </div>
          ))}
        </div>
      </Card>

      {/* Common Use Cases */}
      <Card title="Common Use Cases" className="shadow-2">
        <p className="text-600 mb-4">
          Here are typical scenarios for using the system logs and step-by-step approaches:
        </p>
        
        <div className="grid">
          {useCases.map((useCase, index) => (
            <div key={index} className="col-12 md:col-6">
              <div className="surface-50 border-round p-4 h-full">
                <h5 className="text-primary mb-3">{useCase.title}</h5>
                <p className="text-600 text-sm mb-3">{useCase.description}</p>
                
                <h6 className="text-primary mb-2">Steps:</h6>
                <ol className="text-600 text-sm pl-3 m-0">
                  {useCase.steps.map((step, stepIndex) => (
                    <li key={stepIndex} className="mb-1">{step}</li>
                  ))}
                </ol>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Best Practices */}
      <Card title="Best Practices" className="shadow-2">
        <div className="grid">
          <div className="col-12 md:col-6">
            <h4 className="text-green-600 mb-3">Recommended Practices</h4>
            <ul className="text-600 text-sm pl-3 m-0">
              <li>Use specific search terms for faster results</li>
              <li>Combine filters to narrow down large datasets</li>
              <li>Sort by date when investigating time-based issues</li>
              <li>Refresh data regularly for latest information</li>
              <li>Use appropriate page sizes for your screen</li>
              <li>Clear filters when switching between different searches</li>
              <li>Save or document important log entries for reference</li>
            </ul>
          </div>
          <div className="col-12 md:col-6">
            <h4 className="text-orange-600 mb-3">Performance Tips</h4>
            <ul className="text-600 text-sm pl-3 m-0">
              <li>Use date filters to limit search scope for large datasets</li>
              <li>Avoid very broad search terms that return too many results</li>
              <li>Consider smaller page sizes if experiencing slow loading</li>
              <li>Clear unnecessary filters to improve performance</li>
              <li>Use specific column filters instead of global search when possible</li>
              <li>Refresh data only when necessary to reduce server load</li>
            </ul>
          </div>
        </div>
        
        <Divider />
        
        <Message 
          severity="success" 
          text="System logs are automatically retained according to your organization's data retention policy. Regularly review logs to maintain system health and security awareness."
        />
      </Card>

      {/* Troubleshooting */}
      <Card title="Troubleshooting Guide" className="shadow-2">
        <div className="grid">
          <div className="col-12 md:col-6">
            <h4 className="text-primary mb-3">Logs Not Loading</h4>
            <ol className="text-600 text-sm pl-3 m-0">
              <li>Check your network connection</li>
              <li>Try refreshing the page</li>
              <li>Clear browser cache and cookies</li>
              <li>Verify you have proper access permissions</li>
              <li>Contact administrator if issue persists</li>
            </ol>
          </div>
          <div className="col-12 md:col-6">
            <h4 className="text-primary mb-3">Search Not Working</h4>
            <ol className="text-600 text-sm pl-3 m-0">
              <li>Clear all existing filters first</li>
              <li>Try different search terms or shorter keywords</li>
              <li>Check spelling and try variations</li>
              <li>Use column-specific filters instead of global search</li>
              <li>Refresh data and try search again</li>
            </ol>
          </div>
        </div>

        <Divider />

        <div className="grid">
          <div className="col-12 md:col-6">
            <h4 className="text-primary mb-3">Filters Not Clearing</h4>
            <ol className="text-600 text-sm pl-3 m-0">
              <li>Use the "Clear" button to reset all filters</li>
              <li>Manually clear individual column filters</li>
              <li>Refresh the page to reset everything</li>
              <li>Check for date filters that might still be active</li>
            </ol>
          </div>
          <div className="col-12 md:col-6">
            <h4 className="text-primary mb-3">Date Filter Issues</h4>
            <ol className="text-600 text-sm pl-3 m-0">
              <li>Ensure date format matches system expectations</li>
              <li>Try selecting date using calendar picker</li>
              <li>Clear date filter and reapply</li>
              <li>Check timezone settings if results seem incorrect</li>
            </ol>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default LogsHelp;