import React, { useEffect, useRef, useState } from 'react';
import { Avatar } from 'primereact/avatar';
import { Menu } from 'primereact/menu';
import { useNavigate, useLocation } from 'react-router-dom';
import { useMsal } from '@azure/msal-react';
import logo from '../../assets/Images/Cenovus_logo.png';

const TopMenuBar = () => {
  const menuRight = useRef(null);
  const [initials, setInitials] = useState("");
  const { instance, accounts } = useMsal();
  const isAuthenticated = accounts.length > 0;
  let navigate = useNavigate();
  const location = useLocation();

  // Get current page name for breadcrumb
  const CurrReportName = location.pathname
    .replace('/', '')
    .replaceAll('%20', ' ');

  const SignOut = () => {
    instance.logoutRedirect({
      postLogoutRedirectUri: window.location.origin,
    });
  };

  const ViewGuides = () => {
    navigate('/User Guides');
  };

  const handleSignIn = () => {
    instance.loginRedirect();
  };

  // Set user initials
  useEffect(() => {
    if (isAuthenticated) {
      const account = accounts[0];
      const name = account.name;
      const parts = name.split(",").map((part) => part.trim());
      const tempInitials = parts
        .reverse()
        .map((part) =>
          part
            .split(" ")
            .map((n) => n[0])
            .join("")
        )
        .join("");
      setInitials(tempInitials);
    }
  }, [isAuthenticated, accounts]);

  const items = [
    {
      label: 'Options',
      items: [
        {
          label: 'User Guides',
          icon: 'pi pi-question-circle',
          command: ViewGuides,
        },
        {
          label: 'Sign-Out',
          icon: 'pi pi-sign-out',
          command: SignOut,
        },
      ],
    },
  ];

  // Logo component
  const logoImg = (
    <img
      src={logo}
      alt="Cenovus logo"
      className="bg-no-repeat text-align-left p-0 m-0 ml-2 border-round-md"
      width="210px"
      height="auto"
      onClick={() => navigate('/')}
      style={{ cursor: 'pointer' }}
    />
  );

  return (
    <header className="top-menu" style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      height: '60px',
      zIndex: 20,
      backgroundColor: '#333',
    }}>
      <div className="flex align-items-center" style={{ 
        overflow: 'hidden',
        backgroundColor: '#333',
        padding: '0.5rem 1rem',
        height: '60px'
      }}>
        {/* Logo div */}
        <div className="logo-container">
          {logoImg}
        </div>
        
        {/* Main content area */}
        <div className="grid p-0 m-0" style={{
          flex: 1,
          alignItems: 'center',
        }}>
          <div className="col-11 text-white vertical-align-middle" style={{ 
            fontSize: '22px',
            display: 'flex',
            alignItems: 'center'
          }}>
            <span className="text-3xl" style={{ marginLeft: '3px' }}>&#124;&nbsp;</span>
            AP Invoice Automation <span className="pi pi-angle-right" /> 
            {CurrReportName === '' ? 'Home' : CurrReportName}
          </div>
          <div className="col-1 text-align-right vertical-align-middle p-1 pr-3">
            {isAuthenticated ? (
              <>
                <Menu
                  model={items}
                  popup
                  ref={menuRight}
                  id="popup_menu_right"
                  popupAlignment="right"
                />
                <Avatar
                  label={initials}
                  shape="circle"
                  onClick={(event) => menuRight.current.toggle(event)}
                  aria-haspopup
                  className="m-2"
                  style={{
                    height: '40px',
                    width: '40px',
                    backgroundColor: '#4CAF50',
                    color: 'white',
                    cursor: 'pointer'
                  }}
                />
              </>
            ) : (
              <button 
                onClick={handleSignIn}
                style={{
                  background: '#2F2F2F',
                  color: 'white',
                  border: 'none',
                  padding: '8px 16px',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
              >
                Sign In
              </button>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default TopMenuBar;