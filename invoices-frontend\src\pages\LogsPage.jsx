import React, { useState, useEffect, useCallback } from "react";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { InputText } from "primereact/inputtext";
import { Button } from "primereact/button";
import { Calendar } from "primereact/calendar";
import { Card } from "primereact/card";
import "primereact/resources/themes/lara-light-indigo/theme.css";
import "primereact/resources/primereact.min.css";
import apiService from "../components/api/apiService";
import { useApi } from "../components/api/useApi";
import { useAuth } from "../Auth/useAuth";

export default function LogsPage() {
  const [logs, setLogs] = useState([]);
  const [globalFilterValue, setGlobalFilterValue] = useState('');
  const [filters, setFilters] = useState({
    global: { value: null, matchMode: 'contains' },
    user: { value: null, matchMode: 'contains' },
    message: { value: null, matchMode: 'contains' },
    invoiceNumber: { value: null, matchMode: 'contains' },
    logDate: { value: null, matchMode: 'dateIs' }
  });

  const { getAuthHeaders } = useAuth();
  const { loading: logsLoading, callApi: callLogsApi } = useApi();

  const fetchLogs = useCallback(async () => {
    try {
      const response = await callLogsApi(() => 
        apiService.get('/Logs/GetAllLogs', {}, getAuthHeaders)
      );
      setLogs(response || []);
    } catch (err) {
      console.error("Error fetching logs: ", err);
    }
  }, [callLogsApi, getAuthHeaders]);

  useEffect(() => {
    fetchLogs();
  }, [fetchLogs]);

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      const date = new Date(dateString);
      return isNaN(date.getTime()) 
        ? 'Invalid Date' 
        : date.toLocaleString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          });
    } catch (e) {
      console.error('Error formatting date:', e);
      return 'Invalid Date';
    }
  };

  const onGlobalFilterChange = (e) => {
    const value = e.target.value;
    let _filters = { ...filters };
    _filters['global'].value = value;
    setFilters(_filters);
    setGlobalFilterValue(value);
  };

  const clearFilters = () => {
    setGlobalFilterValue('');
    setFilters({
      global: { value: null, matchMode: 'contains' },
      user: { value: null, matchMode: 'contains' },
      message: { value: null, matchMode: 'contains' },
      invoiceNumber: { value: null, matchMode: 'contains' },
      logDate: { value: null, matchMode: 'dateIs' }
    });
  };

  const dateFilterTemplate = (options) => {
    return (
      <Calendar 
        value={options.value} 
        onChange={(e) => options.filterCallback(e.value, options.index)} 
        dateFormat="mm/dd/yy" 
        placeholder="mm/dd/yyyy"
        mask="99/99/9999"
        showIcon
      />
    );
  };

  const renderHeader = () => {
    return (
      <div className="flex justify-content-between align-items-center">
        <h2 className="m-0">System Logs</h2>
        <div className="flex gap-2">
          <span className="p-input-icon-left">
            <i className="pi pi-search" style = {{paddingLeft: '0.5rem'}} />
            <InputText
              value={globalFilterValue}
              onChange={onGlobalFilterChange}
              placeholder="Search logs..."
              style={{ paddingLeft: '2.5rem' }}
            />
          </span>
          <Button
            type="button"
            icon="pi pi-filter-slash"
            label="Clear"
            outlined
            onClick={clearFilters}
          />
          <Button
            type="button"
            icon="pi pi-refresh"
            label="Refresh"
            onClick={fetchLogs}
            loading={logsLoading}
          />
        </div>
      </div>
    );
  };

  return (
    <div className="p-6">
      <Card>
        <DataTable
          value={logs}
          loading={logsLoading}
          header={renderHeader()}
          filters={filters}
          onFilter={(e) => setFilters(e.filters)}
          globalFilterFields={['user', 'message', 'invoiceNumber']}
          emptyMessage={logsLoading ? "Loading logs..." : "No logs found"}
          paginator
          rows={25}
          rowsPerPageOptions={[10, 25, 50, 100]}
          sortMode="multiple"
          removableSort
          scrollable
          scrollHeight="calc(100vh - 300px)"
          showGridlines
          stripedRows
          resizableColumns
          columnResizeMode="expand"
        >
          <Column 
            field="logDate"
            header="Date" 
            sortable
            filter
            filterElement={dateFilterTemplate}
            body={(rowData) => formatDate(rowData.logDate)}
            style={{ minWidth: '200px' }}
          />
          <Column 
            field="user"
            header="User"
            sortable
            filter
            filterPlaceholder="Search by user"
            style={{ minWidth: '150px' }}
          />
          <Column 
            field="invoiceNumber"
            header="Invoice Number"
            sortable
            filter
            filterPlaceholder="Search by invoice"
            style={{ minWidth: '150px' }}
          />
          <Column 
            field="message"
            header="Message"
            sortable
            filter
            filterPlaceholder="Search in message"
            body={(rowData) => (
              <div style={{ whiteSpace: "pre-wrap" }}>
                {rowData.message}
              </div>
            )}
            style={{ minWidth: '300px' }}
          />
        </DataTable>
      </Card>
    </div>
  );
}
