import React, { useState, useEffect, useRef, useCallback } from "react";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { InputText } from "primereact/inputtext";
import { Button } from "primereact/button";
import { Card } from "primereact/card";
import { Toast } from "primereact/toast";
import { Tag } from "primereact/tag";
import apiService from "../components/api/apiService";
import { useApi } from "../components/api/useApi";
import { useAuth } from "../Auth/useAuth";

export default function IntegrationStatusPage() {
  const [logs, setLogs] = useState([]);
  const [summary, setSummary] = useState(null);
  const [serviceStatus, setServiceStatus] = useState(null);
  const [globalFilterValue, setGlobalFilterValue] = useState("");

  const { getAuthHeaders } = useAuth();
  const { loading: logsLoading, callApi: callLogsApi } = useApi();
  const { loading: summaryLoading, callApi: callSummaryApi } = useApi();
  const { loading: statusLoading, callApi: callStatusApi } = useApi();
  const { loading: runLoading, callApi: callRunApi } = useApi();

  const toast = useRef(null);
  const statusInterval = useRef(null);

  const fetchLogs = useCallback(async () => {
    try {
      const params = new URLSearchParams();
      params.append('maxRecords', '100');

      const response = await callLogsApi(() =>
        apiService.get(`/Integration?${params.toString()}`, {}, getAuthHeaders)
      );
      setLogs(response?.logs || []);
    } catch (err) {
      console.error("Error fetching logs: ", err);
      toast.current?.show({
        severity: "error",
        summary: "Error",
        detail: "Failed to fetch processing logs",
      });
    }
  }, [callLogsApi, getAuthHeaders]);

  const fetchSummary = useCallback(async () => {
    try {
      const response = await callSummaryApi(() =>
        apiService.get("/Integration/summary", {}, getAuthHeaders)
      );
      setSummary(response || null);
    } catch (err) {
      console.error("Error fetching summary: ", err);
      toast.current?.show({
        severity: "error",
        summary: "Error",
        detail: "Failed to fetch processing summary",
      });
    }
  }, [callSummaryApi, getAuthHeaders]);

  const fetchServiceStatus = useCallback(async () => {
    try {
      const response = await callStatusApi(() =>
        apiService.get("/Integration/status", {}, getAuthHeaders)
      );
      setServiceStatus(response || null);
    } catch (err) {
      console.error("Error fetching service status: ", err);
      toast.current?.show({
        severity: "error",
        summary: "Error",
        detail: "Failed to fetch service status",
      });
    }
  }, [callStatusApi, getAuthHeaders]);

  const runServiceManually = async () => {
    try {
      const response = await callRunApi(() =>
        apiService.post("/Integration/run", {}, {}, getAuthHeaders)
      );
      
      if (response?.success) {
        toast.current?.show({
          severity: "success",
          summary: "Success",
          detail: response.message,
        });
        setTimeout(() => {
          fetchServiceStatus();
          fetchLogs();
        }, 1000);
      } else {
        toast.current?.show({
          severity: "warn",
          summary: "Warning",
          detail: response?.message || "Failed to start service",
        });
      }
    } catch (err) {
      console.error("Error running service: ", err);
      toast.current?.show({
        severity: "error",
        summary: "Error",
        detail: "Failed to start integration service",
      });
    }
  };

  // Set up regular status polling
  useEffect(() => {
    fetchServiceStatus();
    
    // Poll status every 30 seconds
    statusInterval.current = setInterval(() => {
      fetchServiceStatus();
    }, 3000);

    return () => {
      if (statusInterval.current) {
        clearInterval(statusInterval.current);
      }
    };
  }, [fetchServiceStatus]);

  useEffect(() => {
    fetchLogs();
    fetchSummary();
  }, [fetchLogs, fetchSummary]);

  const onGlobalFilterChange = (e) => {
    setGlobalFilterValue(e.target.value);
  };

  const logLevelBodyTemplate = (rowData) => {
    const getSeverity = (level) => {
      switch (level?.toUpperCase()) {
        case 'ERROR': return 'danger';
        case 'WARN': return 'warning';
        case 'INFO': return 'info';
        case 'DEBUG': return 'secondary';
        default: return 'secondary';
      }
    };

    return (
      <Tag 
        value={rowData.logLevel} 
        severity={getSeverity(rowData.logLevel)}
      />
    );
  };

  const timestampBodyTemplate = (rowData) => {
    if (!rowData.timestamp) return 'N/A';
    const date = new Date(rowData.timestamp);
    return date.toLocaleString();
  };

  const messageBodyTemplate = (rowData) => {
    const message = rowData.message || '';
    return (
      <div title={message} style={{ maxWidth: '300px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
        {message}
      </div>
    );
  };

  const getStatusSeverity = (status) => {
    switch (status?.toUpperCase()) {
      case 'RUNNING': return 'success';
      case 'IDLE': return 'secondary';
      case 'ERROR': return 'danger';
      default: return 'secondary';
    }
  };

  const renderTopControls = () => {
    return (
      <Card className="mb-4">
        <div className="flex justify-content-between align-items-center">
          <div className="flex align-items-center gap-4">
            <Button
              type="button"
              icon="pi pi-play"
              label="Start Integration"
              onClick={runServiceManually}
              loading={runLoading}
              disabled={serviceStatus?.isRunning || statusLoading}
              severity={serviceStatus?.isRunning ? "secondary" : "success"}
              size="large"
            />
            
            {serviceStatus && (
              <div className="flex align-items-center gap-2">
                <Tag 
                  value={serviceStatus.status} 
                  severity={getStatusSeverity(serviceStatus.status)}
                  icon={serviceStatus.isRunning ? "pi pi-spin pi-spinner" : "pi pi-pause"}
                />
                {serviceStatus.lastRunDate && (
                  <div className="text-500">
                    Last run: {new Date(serviceStatus.lastRunDate).toLocaleString()}
                  </div>
                )}
              </div>
            )}
          </div>
          
          <Button
            type="button"
            icon="pi pi-refresh"
            label="Refresh"
            outlined
            onClick={() => {
              fetchLogs();
              fetchSummary();
              fetchServiceStatus();
            }}
            loading={logsLoading || summaryLoading || statusLoading}
          />
        </div>
      </Card>
    );
  };

  const renderSummaryCards = () => {
    if (!summary) return null;

    return (
      <div className="grid mb-4">
        <div className="col-12 md:col-3">
          <Card>
            <div className="flex justify-content-between align-items-center">
              <div>
                <div className="text-500 font-medium mb-1">Total Cycles</div>
                <div className="text-900 font-bold text-xl">{summary.totalCycles}</div>
              </div>
              <div className="bg-blue-100 text-blue-900 p-2 border-round">
                <i className="pi pi-sync text-xl"></i>
              </div>
            </div>
          </Card>
        </div>
        
        <div className="col-12 md:col-3">
          <Card>
            <div className="flex justify-content-between align-items-center">
              <div>
                <div className="text-500 font-medium mb-1">Successful</div>
                <div className="text-900 font-bold text-xl text-green-500">{summary.successfulCycles}</div>
              </div>
              <div className="bg-green-100 text-green-900 p-2 border-round">
                <i className="pi pi-check-circle text-xl"></i>
              </div>
            </div>
          </Card>
        </div>
        
        <div className="col-12 md:col-3">
          <Card>
            <div className="flex justify-content-between align-items-center">
              <div>
                <div className="text-500 font-medium mb-1">Failed</div>
                <div className="text-900 font-bold text-xl text-red-500">{summary.failedCycles}</div>
              </div>
              <div className="bg-red-100 text-red-900 p-2 border-round">
                <i className="pi pi-times-circle text-xl"></i>
              </div>
            </div>
          </Card>
        </div>
        
        <div className="col-12 md:col-3">
          <Card>
            <div className="flex justify-content-between align-items-center">
              <div>
                <div className="text-500 font-medium mb-1">Records Processed</div>
                <div className="text-900 font-bold text-xl">{summary.totalHeadersCreated?.toLocaleString()}</div>
              </div>
              <div className="bg-purple-100 text-purple-900 p-2 border-round">
                <i className="pi pi-database text-xl"></i>
              </div>
            </div>
          </Card>
        </div>
      </div>
    );
  };

  const renderHeader = () => {
    return (
      <div className="flex justify-content-between align-items-center">
        <h3 className="m-0">Integration Logs</h3>
        <span className="p-input-icon-left">
          <i className="pi pi-search" style={{paddingLeft: '0.5rem'}} />
          <InputText
            value={globalFilterValue}
            onChange={onGlobalFilterChange}
            placeholder="Search logs..."
            style={{ paddingLeft: '2.5rem' }}
          />
        </span>
      </div>
    );
  };

  return (
    <div className="p-6">
      <Toast ref={toast} />
      
      <div className="mb-4">
        <h1 className="text-3xl font-bold text-900 mb-0">Integration Monitoring</h1>
      </div>
      
      {renderTopControls()}
      {renderSummaryCards()}
      
      <Card>
        <DataTable
          value={logs}
          loading={logsLoading}
          header={renderHeader()}
          globalFilter={globalFilterValue}
          globalFilterFields={['logLevel', 'message', 'eventType']}
          emptyMessage={logsLoading ? "Loading logs..." : "No logs found"}
          paginator
          rows={25}
          rowsPerPageOptions={[10, 25, 50, 100]}
          sortMode="multiple"
          removableSort
          scrollable
          scrollHeight="calc(100vh - 500px)"
          showGridlines
          stripedRows
          resizableColumns
          columnResizeMode="expand"
        >
          <Column 
            field="timestamp"
            header="Timestamp"
            body={timestampBodyTemplate}
            sortable
            style={{ minWidth: '180px' }}
          />
          <Column 
            field="logLevel"
            header="Level"
            body={logLevelBodyTemplate}
            sortable
            style={{ minWidth: '100px' }}
          />
          <Column 
            field="eventType"
            header="Event Type"
            sortable
            style={{ minWidth: '150px' }}
          />
          <Column 
            field="message"
            header="Message"
            body={messageBodyTemplate}
            sortable
            style={{ minWidth: '300px' }}
          />
        </DataTable>
      </Card>
    </div>
  );
}