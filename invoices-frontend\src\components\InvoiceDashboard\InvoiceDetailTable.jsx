import React from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';

const detailRowClassName = (rowData) => {
    return rowData.isMatched ? 'matched-detail-row' : '';
};

// Date formatting function
const formatDateOnly = (dateValue) => {
    if (!dateValue) return '';
    
    try {
        const date = new Date(dateValue);
        if (isNaN(date.getTime())) return dateValue; // Return original if invalid date
        
        // Format as YYYY-MM-DD or MM/DD/YYYY - choose your preferred format
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        });
    } catch  {
        return dateValue; // Return original value if formatting fails
    }
};

const InvoiceDetailTable = ({ details }) => {
    // The component now directly uses the 'details' prop, which is expected to be an array.
    return (
        <div className="card mx-4">
            <DataTable 
                value={details || []} // Use the details array directly, defaulting to an empty array
                size="small" 
                dataKey="detailID" 
                rows={10} 
                paginator 
                scrollable 
                scrollHeight="300px" 
                emptyMessage="No details available."
                rowClassName={detailRowClassName}
            >
                <Column field="bol" header="BOL" />
                <Column field="transactionType" header="Transaction Type" />
                <Column field="gravity" header="Gravity" />
                <Column field="origin" header="Origin" />
                <Column field="destination" header="Destination" />
                <Column field="product" header="Product" />
                <Column field="uoM" header="Unit Of Measure" />
                <Column field="quantity" header="Quantity" />
                <Column field="rate" header="Rate" />
                <Column field="amount" header="Amount" body={(rowData) => `${Number(rowData.amount).toFixed(2)}`} />
                <Column field="transactionDate" header="Transaction Date" body={(rowData) => formatDateOnly(rowData.transactionDate)} />
            </DataTable>
        </div>
    );
};

export default InvoiceDetailTable;