import React, { useState } from 'react';
import { Card } from 'primereact/card';
import { Panel } from 'primereact/panel';
import { Badge } from 'primereact/badge';
import { Message } from 'primereact/message';
import { Divider } from 'primereact/divider';
import { Dropdown } from 'primereact/dropdown';
import { Tag } from 'primereact/tag';

const IntegrationStatusHelp = () => {
  const [selectedService, setSelectedService] = useState('Running');

  // Button component that matches the toolbar style exactly
  const ActionButton = ({ label, disabled = false, loading = false, severity = 'primary' }) => {
    const isDisabled = disabled || loading;
    return (
      <button
        type="button"
        disabled={isDisabled}
        className={`p-button p-button-raised p-button-${severity}`}
        style={{
          border: '1px solid black',
          backgroundColor: severity === 'success' ? '#10b981' : severity === 'secondary' ? '#6b7280' : 'white',
          color: severity === 'success' || severity === 'secondary' ? 'white' : 'black',
          opacity: isDisabled ? 0.5 : 1,
          pointerEvents: isDisabled ? 'none' : 'auto',
          cursor: isDisabled ? 'not-allowed' : 'pointer',
          marginRight: '8px'
        }}
      >
        {loading ? <i className="pi pi-spin pi-spinner" style={{ fontSize: '1rem' }}></i> : label}
      </button>
    );
  };

  // Status tag component
  const StatusTag = ({ status, isRunning = false }) => {
    const getSeverity = (status) => {
      switch (status?.toUpperCase()) {
        case 'RUNNING': return 'success';
        case 'IDLE': return 'secondary';
        case 'ERROR': return 'danger';
        default: return 'secondary';
      }
    };

    return (
      <Tag 
        value={status} 
        severity={getSeverity(status)}
        icon={isRunning ? "pi pi-spin pi-spinner" : "pi pi-pause"}
      />
    );
  };

  const getServiceConfig = (status) => {
    switch (status) {
      case "Running":
        return {
          startButton: { disabled: true, label: "Start Integration", severity: "secondary" },
          statusTag: { status: "Running", isRunning: true }
        };
      case "Idle":
        return {
          startButton: { disabled: false, label: "Start Integration", severity: "success" },
          statusTag: { status: "Idle", isRunning: false }
        };
      case "Error":
        return {
          startButton: { disabled: false, label: "Start Integration", severity: "success" },
          statusTag: { status: "Error", isRunning: false }
        };
      default:
        return {};
    }
  };

  const controlFunctions = [
    {
      title: 'Start Integration',
      icon: 'pi pi-play',
      description: 'Manually trigger the data intake and matching integration process.',
      usage: 'Click to start the integration service immediately, regardless of scheduled timing.',
      statusBehavior: {
        'Running': 'Disabled - Integration is currently running',
        'Idle': 'Available - Start the integration process',
        'Error': 'Available - Restart after error condition'
      },
      requirements: ['Service must not be currently running'],
      notes: 'Starting integration will begin processing all pending invoice data and matching records.'
    },
    {
      title: 'Refresh Status',
      icon: 'pi pi-refresh',
      description: 'Update all displayed information including logs, summary statistics, and service status.',
      usage: 'Click to get the latest information from the server without waiting for automatic updates.',
      statusBehavior: {
        'All States': 'Always Available - Refresh current data'
      },
      requirements: ['None - Always available'],
      notes: 'Refresh updates logs, summary cards, and service status simultaneously.'
    }
  ];

  const monitoringFeatures = [
    {
      title: 'Service Status Indicator',
      icon: 'pi pi-info-circle',
      description: 'Real-time display of integration service status with automatic polling.',
      details: [
        'Running - Service is actively processing data',
        'Idle - Service is waiting for next scheduled run or manual trigger',
        'Error - Service encountered an issue and needs attention'
      ],
      updateFrequency: 'Updates every 30 seconds automatically',
      visualCues: 'Color-coded status tags with spinning indicator for active processing'
    },
    {
      title: 'Summary Statistics',
      icon: 'pi pi-chart-bar',
      description: 'Key performance metrics displayed in dashboard cards.',
      metrics: [
        'Total Cycles - Number of integration cycles completed',
        'Successful Cycles - Successfully completed processing cycles',
        'Failed Cycles - Cycles that encountered errors',
        'Records Processed - Total number of invoice headers created'
      ],
      notes: 'Statistics reflect historical performance and current processing results'
    },
    {
      title: 'Integration Logs',
      icon: 'pi pi-list',
      description: 'Detailed log entries showing integration processing activities and events.',
      features: [
        'Real-time log display with automatic refresh',
        'Log level filtering (Error, Warning, Info, Debug)',
        'Global search across all log fields',
        'Sortable columns with timestamp ordering',
        'Pagination for large log volumes'
      ],
      logLevels: {
        'Error': 'Critical issues that stopped processing',
        'Warning': 'Issues that were handled but need attention',
        'Info': 'Normal processing information',
        'Debug': 'Detailed technical information for troubleshooting'
      }
    }
  ];

  const statusDefinitions = [
    { 
      status: 'Running', 
      description: 'Integration service is actively processing invoice data and performing matching operations',
      color: 'success',
      icon: 'pi pi-spin pi-spinner',
      implications: 'New data is being processed, start button is disabled'
    },
    { 
      status: 'Idle', 
      description: 'Service is waiting for next scheduled run or manual trigger',
      color: 'secondary',
      icon: 'pi pi-pause',
      implications: 'Ready to process data, start button is available'
    },
    { 
      status: 'Error', 
      description: 'Service encountered an error and requires attention before continuing',
      color: 'danger',
      icon: 'pi pi-exclamation-triangle',
      implications: 'Processing stopped, check logs for details, manual restart available'
    }
  ];

  const troubleshootingGuide = [
    {
      issue: 'Integration Not Starting',
      symptoms: ['Start button remains disabled', 'Status shows Error', 'No new logs appearing'],
      solutions: [
        'Check service status indicator for error conditions',
        'Review recent error logs for specific issues',
        'Verify system resources and database connectivity',
        'Contact system administrator if problems persist'
      ]
    },
    {
      issue: 'Processing Taking Too Long',
      symptoms: ['Status stuck on Running for extended period', 'No progress in logs', 'Records count not increasing'],
      solutions: [
        'Check recent logs for processing bottlenecks',
        'Monitor system resource usage',
        'Verify database performance and connectivity',
        'Consider data volume and complexity factors'
      ]
    },
    {
      issue: 'Frequent Processing Errors',
      symptoms: ['High failed cycle count', 'Many error-level log entries', 'Inconsistent processing results'],
      solutions: [
        'Review error logs for common patterns',
        'Check data quality and format consistency',
        'Verify configuration settings',
        'Ensure adequate system resources are available'
      ]
    }
  ];

  const serviceOptions = statusDefinitions.map(s => ({ label: s.status, value: s.status }));
  const currentServiceConfig = getServiceConfig(selectedService);

  return (
    <div className="flex flex-column gap-4">
      {/* Header */}
      <Card className="shadow-2">
        <div className="bg-primary text-primary-50 p-4 border-round">
          <h1 className="text-3xl font-bold m-0 mb-2">Integration Monitoring Help</h1>
          <p className="text-lg m-0 text-primary-100">
            Monitor data intake and matching process status, logs, and performance metrics
          </p>
        </div>
      </Card>

      {/* Interactive Service Demo */}
      <Card title="Interactive Service Status Demo" className="shadow-2">
        <div className="mb-4">
          <label className="block font-medium mb-2">Select a service status to see control behavior:</label>
          <Dropdown 
            value={selectedService} 
            options={serviceOptions} 
            onChange={(e) => setSelectedService(e.value)}
            className="w-12rem"
          />
        </div>
        
        <div className="p-4 surface-100 border-round">
          <div className="flex justify-content-between align-items-center">
            <div className="flex gap-2 align-items-center flex-wrap">
              <ActionButton 
                label={currentServiceConfig.startButton?.label || "Start Integration"} 
                disabled={currentServiceConfig.startButton?.disabled}
                severity={currentServiceConfig.startButton?.severity}
              />
              
              <StatusTag 
                status={currentServiceConfig.statusTag?.status} 
                isRunning={currentServiceConfig.statusTag?.isRunning}
              />
              
              <div className="text-500 text-sm">
                Last run: {new Date().toLocaleString()}
              </div>
            </div>
            
            <ActionButton label="Refresh" />
          </div>
        </div>
        
        <Message 
          severity="info" 
          text={`Service Status: ${selectedService} - Controls shown above reflect their actual behavior for this status.`}
          className="mt-3"
        />
      </Card>

      {/* Control Functions */}
      <Card title="Control Functions" className="shadow-2">
        <Message 
          severity="info" 
          text="Control functions allow manual management of the integration service and data refresh."
          className="mb-4"
        />
        
        <div className="grid">
          {controlFunctions.map((func, index) => (
            <div key={index} className="col-12">
              <Panel 
                header={
                  <div className="flex align-items-center">
                    <i className={`${func.icon} text-primary mr-2`}></i>
                    <span className="font-semibold mr-3">{func.title}</span>
                    <ActionButton 
                      label={func.title} 
                      disabled={func.title === 'Start Integration' && selectedService === 'Running'}
                      severity={func.title === 'Start Integration' ? 'success' : 'primary'}
                    />
                  </div>
                } 
                className="mb-3"
                toggleable
              >
                <div className="grid">
                  <div className="col-12 md:col-8">
                    <p className="text-700 mb-3">{func.description}</p>
                    
                    <div className="mb-3">
                      <h5 className="text-primary mb-2">Usage:</h5>
                      <p className="text-600 text-sm m-0">{func.usage}</p>
                    </div>

                    <div className="mb-3">
                      <h5 className="text-primary mb-2">Requirements:</h5>
                      <ul className="text-600 text-sm pl-3 m-0">
                        {func.requirements.map((req, reqIndex) => (
                          <li key={reqIndex}>{req}</li>
                        ))}
                      </ul>
                    </div>

                    {func.notes && (
                      <div className="surface-200 border-round p-3 mt-3">
                        <Message 
                          severity="info" 
                          text={`Note: ${func.notes}`}
                        />
                      </div>
                    )}
                  </div>
                  
                  <div className="col-12 md:col-4">
                    <div className="surface-100 border-round p-3">
                      <h5 className="text-primary mb-2">Status Behavior</h5>
                      <div className="flex flex-column gap-1">
                        {Object.entries(func.statusBehavior).map(([status, behavior], behaviorIndex) => (
                          <div key={behaviorIndex} className="text-sm">
                            <Badge value={status} severity="secondary" className="mr-2" />
                            <span className="text-600">{behavior}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </Panel>
            </div>
          ))}
        </div>
      </Card>

      {/* Monitoring Features */}
      <Card title="Monitoring Features" className="shadow-2">
        <p className="text-600 mb-4">
          The Integration Status page provides comprehensive monitoring capabilities for the data intake and matching process.
        </p>
        
        <div className="grid">
          {monitoringFeatures.map((feature, index) => (
            <div key={index} className="col-12">
              <div className="surface-50 border-round p-4 mb-3 border-left-3 border-blue-500">
                <div className="flex align-items-center mb-3">
                  <i className={`${feature.icon} text-lg text-blue-500 mr-2`}></i>
                  <h4 className="m-0">{feature.title}</h4>
                </div>
                
                <p className="text-600 text-sm mb-3">{feature.description}</p>
                
                {feature.details && (
                  <div className="mb-3">
                    <h5 className="text-primary text-sm mb-2">Status Details:</h5>
                    <ul className="text-600 text-sm pl-3 m-0">
                      {feature.details.map((detail, detailIndex) => (
                        <li key={detailIndex}>{detail}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {feature.metrics && (
                  <div className="mb-3">
                    <h5 className="text-primary text-sm mb-2">Metrics Displayed:</h5>
                    <ul className="text-600 text-sm pl-3 m-0">
                      {feature.metrics.map((metric, metricIndex) => (
                        <li key={metricIndex}>{metric}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {feature.features && (
                  <div className="mb-3">
                    <h5 className="text-primary text-sm mb-2">Features:</h5>
                    <ul className="text-600 text-sm pl-3 m-0">
                      {feature.features.map((feat, featIndex) => (
                        <li key={featIndex}>{feat}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {feature.logLevels && (
                  <div className="mb-3">
                    <h5 className="text-primary text-sm mb-2">Log Levels:</h5>
                    <div className="grid">
                      {Object.entries(feature.logLevels).map(([level, desc], levelIndex) => (
                        <div key={levelIndex} className="col-12 md:col-6 mb-2">
                          <div className="flex align-items-center">
                            <Tag 
                              value={level} 
                              severity={level === 'Error' ? 'danger' : level === 'Warning' ? 'warning' : level === 'Info' ? 'info' : 'secondary'} 
                              className="mr-2" 
                            />
                            <span className="text-600 text-sm">{desc}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {(feature.updateFrequency || feature.visualCues || feature.notes) && (
                  <div className="surface-100 border-round p-2 mt-3">
                    {feature.updateFrequency && (
                      <p className="text-600 text-sm m-0 mb-1"><strong>Update Frequency:</strong> {feature.updateFrequency}</p>
                    )}
                    {feature.visualCues && (
                      <p className="text-600 text-sm m-0 mb-1"><strong>Visual Cues:</strong> {feature.visualCues}</p>
                    )}
                    {feature.notes && (
                      <p className="text-600 text-sm m-0"><strong>Note:</strong> {feature.notes}</p>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Service Status Reference */}
      <Card title="Service Status Reference" className="shadow-2">
        <p className="text-600 mb-4">
          Understanding integration service status indicators and their implications:
        </p>
        
        <div className="grid">
          {statusDefinitions.map((statusDef, index) => (
            <div key={index} className="col-12 md:col-6 lg:col-4">
              <div className="surface-100 border-round p-3 mb-3">
                <div className="flex align-items-center mb-2">
                  <Tag value={statusDef.status} severity={statusDef.color} icon={statusDef.icon} className="mr-2" />
                </div>
                <p className="text-600 text-sm mb-2">{statusDef.description}</p>
                <div className="text-500 text-xs">
                  <strong>Implications:</strong> {statusDef.implications}
                </div>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Troubleshooting Guide */}
      <Card title="Troubleshooting Guide" className="shadow-2">
        <p className="text-600 mb-4">
          Common issues and their resolution steps for integration monitoring:
        </p>
        
        <div className="grid">
          {troubleshootingGuide.map((guide, index) => (
            <div key={index} className="col-12">
              <Panel 
                header={
                  <div className="flex align-items-center">
                    <i className="pi pi-exclamation-triangle text-orange-500 mr-2"></i>
                    <span className="font-semibold">{guide.issue}</span>
                  </div>
                } 
                className="mb-3"
                toggleable
                collapsed
              >
                <div className="grid">
                  <div className="col-12 md:col-6">
                    <h5 className="text-orange-600 mb-2">Symptoms:</h5>
                    <ul className="text-600 text-sm pl-3 m-0 mb-3">
                      {guide.symptoms.map((symptom, symIndex) => (
                        <li key={symIndex}>{symptom}</li>
                      ))}
                    </ul>
                  </div>
                  
                  <div className="col-12 md:col-6">
                    <h5 className="text-green-600 mb-2">Solutions:</h5>
                    <ul className="text-600 text-sm pl-3 m-0">
                      {guide.solutions.map((solution, solIndex) => (
                        <li key={solIndex}>{solution}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </Panel>
            </div>
          ))}
        </div>
      </Card>

      {/* Usage Guidelines */}
      <Card title="Usage Guidelines" className="shadow-2">
        <div className="grid">
          <div className="col-12 md:col-6">
            <h4 className="text-green-600 mb-3">Best Practices</h4>
            <ul className="text-600 text-sm pl-3 m-0">
              <li>Monitor service status regularly during business hours</li>
              <li>Check summary statistics daily for processing trends</li>
              <li>Review error logs immediately when issues occur</li>
              <li>Use manual start only when necessary, respect scheduled runs</li>
              <li>Keep logs filtered to relevant time periods for performance</li>
              <li>Document any manual interventions in system notes</li>
              <li>Set up alerts for critical error conditions</li>
            </ul>
          </div>
          <div className="col-12 md:col-6">
            <h4 className="text-orange-600 mb-3">Important Considerations</h4>
            <ul className="text-600 text-sm pl-3 m-0">
              <li>Service status updates every 30 seconds automatically</li>
              <li>Manual start will interrupt scheduled processing cycles</li>
              <li>Large data volumes may require extended processing time</li>
              <li>Error status requires investigation before restart</li>
              <li>Log search is case-sensitive and searches all visible fields</li>
              <li>Refresh button updates all page data simultaneously</li>
              <li>Processing metrics reflect historical and current cycles</li>
            </ul>
          </div>
        </div>
        
        <Divider />
        
        <div className="grid">
          <div className="col-12 md:col-6">
            <Message 
              severity="info" 
              text="Status indicators update automatically - manual refresh is only needed for immediate updates or troubleshooting."
            />
          </div>
          <div className="col-12 md:col-6">
            <Message 
              severity="warn" 
              text="Always check error logs before manually restarting a failed integration service."
            />
          </div>
        </div>
      </Card>
    </div>
  );
};

export default IntegrationStatusHelp;