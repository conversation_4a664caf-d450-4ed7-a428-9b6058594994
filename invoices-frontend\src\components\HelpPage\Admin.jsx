import React from 'react';
import { Card } from 'primereact/card';
import { Panel } from 'primereact/panel';
import { Badge } from 'primereact/badge';
import { Message } from 'primereact/message';
import { Divider } from 'primereact/divider';
import { Button } from 'primereact/button';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Tag } from 'primereact/tag';

const AdminHelp = () => {
  const configurationFields = [
    {
      field: 'Counterparty',
      type: 'Dropdown (Required)',
      description: 'The business associate/vendor that this configuration applies to.',
      usage: 'Select the counterparty from the list of available business associates.',
      behavior: 'Cannot be changed after creation. Used as the primary matching key for incoming invoices.',
      validation: 'Required field. Must select from existing counterparty records.'
    },
    {
      field: 'Invoice Header Type',
      type: 'Dropdown (Required)',
      description: 'Template/format that should be applied to invoices from this counterparty.',
      usage: 'Determines invoice layout, required fields, and processing rules.',
      behavior: 'Can be changed after creation. Applied to all matching invoices.',
      validation: 'Required field. Must select from available template options.'
    },
    {
      field: 'Automatic Processing',
      type: 'Checkbox',
      description: 'Enables fully automated processing without manual review.',
      usage: 'Check to allow invoices to be processed automatically when all conditions are met.',
      behavior: 'When enabled, matching invoices bypass manual approval queues.',
      validation: 'Optional. Defaults to disabled for safety.'
    },
    {
      field: 'Default Due Date',
      type: 'Dropdown (Optional)',
      description: 'Payment terms to apply when not specified on the invoice.',
      usage: 'Select standard payment terms like "Net 30", "Due on Receipt", etc.',
      behavior: 'Only applied if invoice does not contain due date information.',
      validation: 'Optional. Can be left blank if not applicable.'
    },
    {
      field: 'Min Threshold',
      type: 'Currency Input',
      description: 'Minimum invoice amount for automatic processing.',
      usage: 'Set the lowest dollar amount that can be processed automatically.',
      behavior: 'Invoices below this amount may require manual approval.',
      validation: 'Must be greater than or equal to 0. Defaults to $0.'
    },
    {
      field: 'Max Threshold',
      type: 'Currency Input',
      description: 'Maximum invoice amount for automatic processing.',
      usage: 'Set the highest dollar amount that can be processed automatically.',
      behavior: 'Invoices above this amount will require manual approval.',
      validation: 'Must be greater than or equal to Min Threshold.'
    }
  ];

  const processingSteps = [
    {
      step: 1,
      title: 'Invoice Receipt',
      description: 'System receives invoice from external source or integration.',
      details: 'Invoice data is parsed and basic validation is performed.'
    },
    {
      step: 2,
      title: 'Counterparty Matching',
      description: 'System identifies which counterparty configuration applies.',
      details: 'Uses counterparty name, ID, or other identifiers to find matching configuration.'
    },
    {
      step: 3,
      title: 'Configuration Application',
      description: 'Default values from configuration are applied to invoice fields.',
      details: 'Only fills in missing fields - existing invoice data takes precedence.'
    },
    {
      step: 4,
      title: 'Threshold Validation',
      description: 'Invoice amount is checked against min/max thresholds.',
      details: 'Determines if automatic processing is allowed based on amount limits.'
    },
    {
      step: 5,
      title: 'Processing Decision',
      description: 'System decides between automatic processing or manual review.',
      details: 'Based on automatic flag, thresholds, and data completeness.'
    }
  ];

  const actions = [
    {
      action: 'Add Configuration',
      icon: 'pi pi-plus',
      description: 'Create a new counterparty configuration with default processing rules.',
      usage: 'Click "Add Config" button to open the configuration dialog.',
      behavior: [
        'Opens dialog with empty form fields',
        'Loads all available counterparty options',
        'Requires counterparty and header type selection',
        'Saves configuration and refreshes table'
      ],
      requirements: ['User must have write permissions', 'Counterparty cannot already have a configuration']
    },
    {
      action: 'Edit Configuration',
      icon: 'pi pi-pencil',
      description: 'Modify existing counterparty configuration settings.',
      usage: 'Click pencil icon on any row to edit that configuration.',
      behavior: [
        'Opens dialog pre-populated with current values',
        'Counterparty field is disabled (cannot be changed)',
        'All other fields can be modified',
        'Updates existing record when saved'
      ],
      requirements: ['User must have write permissions']
    },
    {
      action: 'Delete Configuration',
      icon: 'pi pi-trash',
      description: 'Remove counterparty configuration permanently.',
      usage: 'Click trash icon and confirm deletion.',
      behavior: [
        'Shows confirmation dialog with counterparty name',
        'Permanently removes configuration from system',
        'Affects future invoice processing for this counterparty',
        'Cannot be undone once confirmed'
      ],
      requirements: ['User must have delete permissions', 'Confirmation required']
    },
    {
      action: 'Search Configurations',
      icon: 'pi pi-search',
      description: 'Search configurations by counterparty, header type, or due date terms.',
      usage: 'Type in search box to filter configurations.',
      behavior: [
        'Searches counterparty name, header type, and due date fields',
        'Updates results in real-time as you type',
        'Case-insensitive partial matching',
        'Clears when search box is emptied'
      ],
      requirements: ['No special requirements']
    },
    {
      action: 'Refresh Data',
      icon: 'pi pi-refresh',
      description: 'Reload all configuration data from server.',
      usage: 'Click refresh button to get latest data.',
      behavior: [
        'Fetches current configurations from database',
        'Shows loading indicator during refresh',
        'Maintains current search filter after refresh',
        'Useful after other users make changes'
      ],
      requirements: ['No special requirements']
    }
  ];

  const bestPractices = [
    {
      title: 'Start Conservative',
      description: 'Begin with automatic processing disabled and low thresholds.',
      details: 'Enable automation gradually as you gain confidence in the configuration accuracy.'
    },
    {
      title: 'Monitor Processing Results',
      description: 'Regularly review invoices processed with each configuration.',
      details: 'Look for patterns in manual corrections needed and adjust configurations accordingly.'
    },
    {
      title: 'Set Appropriate Thresholds',
      description: 'Balance automation efficiency with risk management.',
      details: 'Higher thresholds require more manual review but reduce financial risk.'
    },
    {
      title: 'Keep Configurations Current',
      description: 'Update configurations when counterparty requirements change.',
      details: 'Business rule changes, new payment terms, or template updates may require configuration changes.'
    }
  ];

  const troubleshootingIssues = [
    {
      issue: 'Configuration Not Being Applied',
      symptoms: 'Invoices from counterparty not using default values',
      causes: [
        'Counterparty name mismatch between invoice and configuration',
        'Invoice already contains all required fields',
        'Cross-reference mapping issues',
        'Configuration is inactive or deleted'
      ],
      solutions: [
        'Verify counterparty name spelling and format',
        'Check cross-reference mappings for counterparty',
        'Review invoice data completeness',
        'Confirm configuration exists and is active'
      ]
    },
    {
      issue: 'Automatic Processing Not Working',
      symptoms: 'Invoices requiring manual approval despite configuration',
      causes: [
        'Automatic processing flag is disabled',
        'Invoice amount outside threshold range',
        'Missing required invoice data',
        'System-level automatic processing disabled'
      ],
      solutions: [
        'Enable automatic processing checkbox',
        'Adjust min/max threshold values',
        'Review invoice data completeness',
        'Check system-wide automation settings'
      ]
    }
  ];

  const headerTemplate = (
    <div className="bg-primary text-primary-50 p-4 border-round">
      <h1 className="text-3xl font-bold m-0 mb-2">Counterparty Configuration Management - User Manual</h1>
      <p className="text-lg m-0 text-primary-100">
        Complete guide to setting up default processing rules for counterparties
      </p>
    </div>
  );

  const fieldRowTemplate = (rowData) => (
    <div className="flex flex-column gap-2">
      <div className="flex align-items-center gap-2">
        <Badge value={rowData.field} severity="info" />
        <Tag value={rowData.type} severity={rowData.field.includes('Required') ? 'danger' : 'success'} />
      </div>
      <div className="text-sm text-600">{rowData.usage}</div>
    </div>
  );

  const currencyTemplate = (value) => {
    return new Intl.NumberFormat('en-US', { 
      style: 'currency', 
      currency: 'USD' 
    }).format(value);
  };

  const exampleConfigurations = [
    {
      counterpartyName: 'ABC Corporation',
      invoiceHeaderType: 'Standard Invoice',
      isAutomatic: true,
      defaultDueDate: 'Net 30',
      minThreshold: 100,
      maxThreshold: 5000
    },
    {
      counterpartyName: 'XYZ Vendor Ltd',
      invoiceHeaderType: 'Detailed Line Items',
      isAutomatic: false,
      defaultDueDate: 'Due on Receipt',
      minThreshold: 0,
      maxThreshold: 10000
    },
    {
      counterpartyName: 'Quick Services Inc',
      invoiceHeaderType: 'Simple Format',
      isAutomatic: true,
      defaultDueDate: 'Net 15',
      minThreshold: 50,
      maxThreshold: 2500
    }
  ];

  return (
    <div className="flex flex-column gap-4">
      {/* Header Section */}
      <Card className="shadow-2">
        {headerTemplate}
      </Card>

      {/* System Overview */}
      <Card title="System Overview" className="shadow-2">
        <Message 
          severity="info" 
          text="The Counterparty Configuration system establishes default processing rules for invoices from specific business partners. When invoices are received, the system automatically applies these defaults to ensure consistent processing and reduce manual data entry."
          className="mb-4"
        />
        
        <div className="grid">
          <div className="col-12 md:col-4">
            <Panel header="Default Value Application" className="h-full">
              <p className="text-600 text-sm mb-3">
                Automatically fills in missing invoice fields using predefined counterparty defaults.
              </p>
              <ul className="text-600 text-sm pl-3 m-0">
                <li>Invoice header templates</li>
                <li>Payment due date terms</li>
                <li>Processing workflow rules</li>
                <li>Validation thresholds</li>
              </ul>
            </Panel>
          </div>

          <div className="col-12 md:col-4">
            <Panel header="Automated Processing" className="h-full">
              <p className="text-600 text-sm mb-3">
                Enables straight-through processing for invoices that meet defined criteria.
              </p>
              <ul className="text-600 text-sm pl-3 m-0">
                <li>Amount threshold controls</li>
                <li>Automatic approval workflows</li>
                <li>Manual review bypassing</li>
                <li>Exception handling rules</li>
              </ul>
            </Panel>
          </div>

          <div className="col-12 md:col-4">
            <Panel header="Quality Assurance" className="h-full">
              <p className="text-600 text-sm mb-3">
                Maintains data consistency and reduces processing errors through standardization.
              </p>
              <ul className="text-600 text-sm pl-3 m-0">
                <li>Consistent field formatting</li>
                <li>Reduced manual errors</li>
                <li>Standardized workflows</li>
                <li>Audit trail maintenance</li>
              </ul>
            </Panel>
          </div>
        </div>
      </Card>

      {/* Configuration Fields Reference */}
      <Card title="Configuration Fields Reference" className="shadow-2">
        <p className="text-600 mb-4">
          Each counterparty configuration consists of the following fields that control how invoices are processed:
        </p>
        
        <DataTable 
          value={configurationFields} 
          className="mb-4"
          showGridlines
          stripedRows
        >
          <Column 
            field="field" 
            header="Field" 
            style={{ width: '20%' }}
            body={fieldRowTemplate}
          />
          <Column 
            field="description" 
            header="Description" 
            style={{ width: '30%' }}
          />
          <Column 
            field="behavior" 
            header="Processing Behavior" 
            style={{ width: '25%' }}
          />
          <Column 
            field="validation" 
            header="Validation Rules" 
            style={{ width: '25%' }}
          />
        </DataTable>
      </Card>

      {/* Processing Flow */}
      <Card title="Invoice Processing Flow" className="shadow-2">
        <p className="text-600 mb-4">
          Understanding how configurations are applied during invoice processing:
        </p>
        
        <div className="grid">
          {processingSteps.map((step, index) => (
            <div key={index} className="col-12">
              <div className="flex align-items-start mb-3">
                <div className="bg-primary text-primary-50 border-round flex align-items-center justify-content-center" 
                     style={{ minWidth: '2rem', minHeight: '2rem', marginRight: '1rem' }}>
                  <span className="font-bold">{step.step}</span>
                </div>
                <div className="flex-1">
                  <h5 className="text-primary m-0 mb-2">{step.title}</h5>
                  <p className="text-700 text-sm mb-2">{step.description}</p>
                  <p className="text-600 text-sm m-0">{step.details}</p>
                </div>
              </div>
              {index < processingSteps.length - 1 && <Divider />}
            </div>
          ))}
        </div>

        <Message 
          severity="warn" 
          text="Configuration values only fill in missing invoice fields. Existing invoice data always takes precedence over configured defaults."
          className="mt-4"
        />
      </Card>

      {/* Available Actions */}
      <Card title="Available Actions" className="shadow-2">
        <p className="text-600 mb-4">
          The admin screen provides comprehensive tools for managing counterparty configurations:
        </p>
        
        <div className="grid">
          {actions.map((action, index) => (
            <div key={index} className="col-12">
              <Panel 
                header={
                  <div className="flex align-items-center">
                    <i className={`${action.icon} text-primary mr-2`}></i>
                    <span className="font-semibold">{action.action}</span>
                  </div>
                } 
                className="mb-3"
                toggleable
              >
                <div className="grid">
                  <div className="col-12 md:col-8">
                    <p className="text-700 mb-3">{action.description}</p>
                    
                    <div className="mb-3">
                      <h5 className="text-primary mb-2">Usage:</h5>
                      <p className="text-600 text-sm m-0">{action.usage}</p>
                    </div>

                    <div className="mb-3">
                      <h5 className="text-primary mb-2">Requirements:</h5>
                      <ul className="text-600 text-sm pl-3 m-0">
                        {action.requirements.map((req, reqIndex) => (
                          <li key={reqIndex}>{req}</li>
                        ))}
                      </ul>
                    </div>

                    <div className="surface-100 border-round p-3">
                      <h5 className="text-primary mb-2">Behavior</h5>
                      <ul className="text-600 text-sm pl-3 m-0">
                        {action.behavior.map((behavior, behaviorIndex) => (
                          <li key={behaviorIndex}>{behavior}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                  
                  <div className="col-12 md:col-4">
                    <div className="surface-50 border-round p-3 text-center">
                      <h6 className="text-primary mb-3">Button Examples</h6>
                      <div className="flex flex-column gap-3 align-items-center">
                        {action.action === 'Add Configuration' && (
                          <>
                            <Button 
                              icon="pi pi-plus" 
                              label="Add Config"
                              className="p-button-sm"
                            />
                            <span className="text-sm text-600">Add New Config</span>
                          </>
                        )}
                        
                        {action.action === 'Edit Configuration' && (
                          <>
                            <Button 
                              icon="pi pi-pencil" 
                              className="p-button-sm" 
                              rounded
                              outlined
                              tooltip="Edit configuration"
                            />
                            <span className="text-sm text-600">Edit Button</span>
                          </>
                        )}
                        
                        {action.action === 'Delete Configuration' && (
                          <>
                            <Button 
                              icon="pi pi-trash" 
                              className="p-button-danger p-button-sm" 
                              rounded
                              outlined
                              tooltip="Delete configuration"
                            />
                            <span className="text-sm text-600">Delete Button</span>
                          </>
                        )}
                        
                        {action.action === 'Refresh Data' && (
                          <>
                            <Button 
                              icon="pi pi-refresh" 
                              label="Refresh"
                              className="p-button-sm"
                              outlined
                            />
                            <span className="text-sm text-600">Refresh Button</span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </Panel>
            </div>
          ))}
        </div>
      </Card>

      {/* Example Configurations */}
      <Card title="Example Configurations" className="shadow-2">
        <p className="text-600 mb-4">
          Here are examples of typical counterparty configurations and their use cases:
        </p>
        
        <DataTable 
          value={exampleConfigurations} 
          className="mb-4"
          showGridlines
          stripedRows
        >
          <Column 
            field="counterpartyName" 
            header="Counterparty" 
            style={{ minWidth: '160px' }}
          />
          <Column 
            field="invoiceHeaderType" 
            header="Header Type" 
            style={{ minWidth: '140px' }}
          />
          <Column 
            field="isAutomatic" 
            header="Automatic" 
            body={(rowData) => (
              <Tag 
                value={rowData.isAutomatic ? 'Yes' : 'No'} 
                severity={rowData.isAutomatic ? 'success' : 'danger'}
              />
            )}
            style={{ minWidth: '100px' }}
          />
          <Column 
            field="defaultDueDate" 
            header="Due Terms" 
            style={{ minWidth: '120px' }}
          />
          <Column 
            field="minThreshold" 
            header="Min Threshold" 
            body={(rowData) => currencyTemplate(rowData.minThreshold)}
            style={{ minWidth: '120px' }}
          />
          <Column 
            field="maxThreshold" 
            header="Max Threshold" 
            body={(rowData) => currencyTemplate(rowData.maxThreshold)}
            style={{ minWidth: '120px' }}
          />
        </DataTable>

        <div className="grid mt-3">
          <div className="col-12 md:col-4">
            <div className="surface-50 border-round p-3">
              <h5 className="text-primary mb-2">ABC Corporation</h5>
              <p className="text-600 text-sm m-0">
                High-volume, trusted vendor with automatic processing enabled for invoices between $100-$5,000.
              </p>
            </div>
          </div>
          <div className="col-12 md:col-4">
            <div className="surface-50 border-round p-3">
              <h5 className="text-primary mb-2">XYZ Vendor Ltd</h5>
              <p className="text-600 text-sm m-0">
                New vendor requiring manual review for all invoices until trust is established.
              </p>
            </div>
          </div>
          <div className="col-12 md:col-4">
            <div className="surface-50 border-round p-3">
              <h5 className="text-primary mb-2">Quick Services Inc</h5>
              <p className="text-600 text-sm m-0">
                Small service provider with fast payment terms and low-risk automatic processing.
              </p>
            </div>
          </div>
        </div>
      </Card>

      {/* Best Practices */}
      <Card title="Best Practices" className="shadow-2">
        <div className="grid">
          {bestPractices.map((practice, index) => (
            <div key={index} className="col-12 md:col-6">
              <div className="surface-50 border-round p-3 border-left-3 border-green-500 h-full">
                <h5 className="m-0 mb-2 text-green-600">{practice.title}</h5>
                <p className="text-600 text-sm mb-2">{practice.description}</p>
                <p className="text-600 text-sm m-0 font-italic">{practice.details}</p>
              </div>
            </div>
          ))}
        </div>
        
        <Divider />
        
        <Message 
          severity="success" 
          text="Start with conservative settings and gradually increase automation as you gain confidence in each counterparty's invoice consistency and your configuration accuracy."
        />
      </Card>

      {/* Troubleshooting */}
      <Card title="Troubleshooting Guide" className="shadow-2">
        <div className="grid">
          {troubleshootingIssues.map((issue, index) => (
            <div key={index} className="col-12">
              <Panel 
                header={issue.issue}
                className="mb-3"
                toggleable
              >
                <div className="grid">
                  <div className="col-12 md:col-4">
                    <h5 className="text-orange-600 mb-2">Symptoms</h5>
                    <p className="text-600 text-sm">{issue.symptoms}</p>
                  </div>
                  <div className="col-12 md:col-4">
                    <h5 className="text-red-600 mb-2">Common Causes</h5>
                    <ul className="text-600 text-sm pl-3 m-0">
                      {issue.causes.map((cause, causeIndex) => (
                        <li key={causeIndex}>{cause}</li>
                      ))}
                    </ul>
                  </div>
                  <div className="col-12 md:col-4">
                    <h5 className="text-green-600 mb-2">Solutions</h5>
                    <ul className="text-600 text-sm pl-3 m-0">
                      {issue.solutions.map((solution, solutionIndex) => (
                        <li key={solutionIndex}>{solution}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </Panel>
            </div>
          ))}
        </div>

        <Message 
          severity="warn" 
          text="When troubleshooting configuration issues, always check the cross-reference mappings first, as counterparty name mismatches are the most common cause of configuration not being applied."
          className="mt-3"
        />
      </Card>

      {/* Security and Permissions */}
      <Card title="Security and Permissions" className="shadow-2">
        <div className="grid">
          <div className="col-12 md:col-6">
            <h4 className="text-primary mb-3">Required Permissions</h4>
            <ul className="text-600 text-sm pl-3 m-0">
              <li><strong>Read Access:</strong> View existing configurations and dropdown options</li>
              <li><strong>Write Access:</strong> Create and modify counterparty configurations</li>
              <li><strong>Delete Access:</strong> Remove configurations (separate permission)</li>
              <li><strong>Admin Access:</strong> Modify system-wide automation settings</li>
            </ul>
          </div>
          <div className="col-12 md:col-6">
            <h4 className="text-primary mb-3">Audit Considerations</h4>
            <ul className="text-600 text-sm pl-3 m-0">
              <li>All configuration changes are logged with user and timestamp</li>
              <li>Deletion events are recorded for compliance</li>
              <li>Automatic processing decisions are auditable</li>
              <li>Threshold breaches are tracked and reported</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default AdminHelp;