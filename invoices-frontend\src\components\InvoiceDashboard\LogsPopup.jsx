import React, { useState, useEffect, useCallback } from "react";
import { Dialog } from "primereact/dialog";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import "primereact/resources/themes/lara-light-indigo/theme.css";
import "primereact/resources/primereact.min.css";
import apiService from "../api/apiService";
import { useApi } from "../api/useApi";
import { useAuth } from "../../Auth/useAuth";

const LogsPopup = ({ visible, onHide, headerID }) => {
  const [logs, setLogs] = useState([]);
  const { getAuthHeaders } = useAuth();
  const { loading: logsLoading, callApi: callLogsApi } = useApi();

  const fetchLogs = useCallback(async () => {
    try {
      const response = await callLogsApi(() =>
        apiService.get("/Logs/GetAllLogs", { headerId: headerID }, getAuthHeaders)
      );
      setLogs(response || []);
    } catch (err) {
      console.error("Error fetching logs: ", err);
    }
  }, [callLogsApi, headerID, getAuthHeaders]);

  useEffect(() => {
    if (visible && headerID) {
      fetchLogs();
    }
  }, [visible, headerID, fetchLogs]); // No more warning!

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A'; // Handle null/undefined cases
    
    try {
      const date = new Date(dateString);
      return isNaN(date.getTime()) 
        ? 'Invalid Date' 
        : date.toLocaleString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          });
    } catch (e) {
      console.error('Error formatting date:', e);
      return 'Invalid Date';
    }
  };

  return (
    <Dialog
      header="Logs"
      visible={visible}
      onHide={onHide}
      style={{ width: "50vw" }}
      breakpoints={{ "960px": "75vw", "641px": "90vw" }}
    >
      <div className="flex flex-column gap-3">
        <div>
          <h3>Log History</h3>
          <DataTable
            value={logs}
            loading={logsLoading}
            scrollable
            scrollHeight="400px"
            emptyMessage={logsLoading ? "Loading logs..." : "No logs found"}
          >
            <Column 
              field="logDate"
              header="Date" 
              body={(rowData) => formatDate(rowData.logDate)}
            />
            <Column 
              field="user"
              header="User"
              body={(rowData) => rowData.user}
            />
            <Column 
              field="message"
              header="Message" 
              body={(rowData) => (
                <div style={{ whiteSpace: "pre-wrap" }}>
                  {rowData.message}
                </div>
              )}
            />
          </DataTable>
        </div>
      </div>
    </Dialog>
  );
};

export default LogsPopup;