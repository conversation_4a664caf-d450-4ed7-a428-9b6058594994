import { useEffect, useCallback, useRef } from 'react';
import { useApi } from './useApi';
import apiService from './apiService';
import { useAuth } from '../../Auth/useAuth';
import { useInvoiceContext } from '../InvoiceDashboard/InvoiceHooks';

export const useInvoices = (initialFilters, shouldFetch = true) => {
    const { 
        invoices, 
        details, 
        allBaOptions, 
        loading, 
        setLoading, 
        setData 
    } = useInvoiceContext();
    
    const { error, callApi } = useApi();
    const { getAuthHeaders } = useAuth();
    
    // Use ref to track the current shouldFetch state for the interval
    const shouldFetchRef = useRef(shouldFetch);
    useEffect(() => {
        shouldFetchRef.current = shouldFetch;
    }, [shouldFetch]);

    const fetchData = useCallback(async (currentFilters) => {
        // Only fetch if shouldFetch is true
        if (!shouldFetchRef.current) {
            return null;
        }
        
        setLoading(true);
        
        try {
            const params = {};
            if (currentFilters.counterparty) params.counterparty = currentFilters.counterparty;
            if (currentFilters.invoiceDateFrom) params.startDate = currentFilters.invoiceDateFrom.toISOString().split('T')[0];
            if (currentFilters.invoiceDateTo) params.endDate = currentFilters.invoiceDateTo.toISOString().split('T')[0];
            if (currentFilters.dueDateFrom) params.startDueDate = currentFilters.dueDateFrom.toISOString().split('T')[0];
            if (currentFilters.dueDateTo) params.endDueDate = currentFilters.dueDateTo.toISOString().split('T')[0];
            
            const data = await callApi(() => apiService.get('/Invoices/GetInvoices', params, getAuthHeaders));
            
            if (data?.headers && data?.details) {
                const detailsMap = data.details.reduce((acc, detail) => {
                    if (!acc[detail.headerID]) {
                        acc[detail.headerID] = [];
                    }
                    acc[detail.headerID].push(detail);
                    return acc;
                }, {});

                const processedData = {
                    invoices: data.headers,
                    details: detailsMap,
                    allBaOptions: allBaOptions // Keep existing BA options
                };
                
                // Update context with new data
                setData(processedData);
                
                return processedData;
            } else {
                console.error("API did not return expected data structure", data);
                const emptyData = {
                    invoices: [],
                    details: {},
                    allBaOptions: allBaOptions
                };
                setData(emptyData);
                return emptyData;
            }
        } catch (err) {
            console.error("Error fetching data: ", err);
            const emptyData = {
                invoices: [],
                details: {},
                allBaOptions: allBaOptions
            };
            setData(emptyData);
            return emptyData;
        }
    }, [callApi, getAuthHeaders, setLoading, setData, allBaOptions]);

    // Initial BA options fetch - this can run regardless of shouldFetch since it's one-time initialization
    useEffect(() => {
        const fetchBAsForDashboard = async () => {
            // Only fetch if we don't have BA options yet
            if (allBaOptions.length === 0) {
                try {
                    const data = await callApi(() => apiService.getBAs(getAuthHeaders));
                    if (Array.isArray(data)) {
                        const baOptions = data.map(ba => ({ label: ba.value, value: ba.id }));
                        
                        // Update context with BA options
                        setData({
                            invoices: invoices,
                            details: details,
                            allBaOptions: baOptions
                        });
                    }
                } catch (err) {
                    console.error("Error fetching BAs for Dashboard:", err);
                }
            }
        };
        fetchBAsForDashboard();
    }, [callApi, getAuthHeaders, allBaOptions.length, invoices, details, setData]);

    // Silent Refresh - now respects the shouldFetch parameter
    useEffect(() => {
        const interval = setInterval(() => {
            // Only auto-refresh if shouldFetch is true
            if (shouldFetchRef.current) {
                fetchData(initialFilters);
            }
        }, 120000); // Refresh every 2 minutes
        
        return () => clearInterval(interval);
    }, [fetchData, initialFilters]);

    return {
        invoices,
        details,
        allBaOptions,
        loading,
        error,
        fetchData,
    };
};