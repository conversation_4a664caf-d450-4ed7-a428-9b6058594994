import React, { useState, useEffect, useRef } from "react";
import { Button } from "primereact/button";
import { OverlayPanel } from "primereact/overlaypanel";
import { InputText } from "primereact/inputtext";
import { Calendar } from "primereact/calendar";
import { MultiSelect } from "primereact/multiselect";

const FilterButton = ({ label, filterKey, options, filters, updateFilter, clearFilter, isMulti, isDateRange }) => {
    const op = useRef(null);
    const initialValue = isMulti ? (filters[filterKey] || []) : (isDateRange ? [filters[filterKey + 'From'], filters[filterKey + 'To']] : (filters[filterKey] || ""));
    const [localInputValue, setLocalInputValue] = useState(initialValue);
    
    useEffect(() => {
        const initialValue = isMulti
            ? (filters[filterKey] || [])
            : isDateRange
            ? [filters[filterKey + 'From'], filters[filterKey + 'To']]
            : filters[filterKey] || "";

        setLocalInputValue(initialValue);
    }, [filters, filterKey, isMulti, isDateRange]);

    let content;
    const applyButtonNeeded = !isMulti && !isDateRange;

    if (isDateRange) {
        content = (
            <>
                <Calendar value={filters[filterKey + 'From']} onChange={(e) => updateFilter(filterKey + 'From', e.value)} placeholder="From" dateFormat="yy-mm-dd" className="w-full mb-1" />
                <Calendar value={filters[filterKey + 'To']} onChange={(e) => updateFilter(filterKey + 'To', e.value)} placeholder="To" dateFormat="yy-mm-dd" className="w-full" />
            </>
        );
    } else if (isMulti) {
        content = <MultiSelect value={filters[filterKey]} options={options} onChange={(e) => updateFilter(filterKey, e.value)} placeholder="Select" className="w-full" />;
    } else {
        content = (
            <InputText
                value={localInputValue}
                onChange={(e) => setLocalInputValue(e.target.value)}
                placeholder="Search..."
                className="w-full"
                onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        updateFilter(filterKey, localInputValue);
                        op.current.hide();
                    }
                }}
            />
        );
    }

    const fieldsToClear = isDateRange ? [filterKey + 'From', filterKey + 'To'] : [filterKey];

    return (
        <div className="flex align-items-center gap-1">
            <span>{label}</span>
            <Button type="button" icon="pi pi-filter" className="p-button-text p-0" onClick={(e) => op.current.toggle(e)} />
            <OverlayPanel ref={op} dismissable showCloseIcon>
                <div className="flex flex-column gap-2 p-3" style={{ minWidth: '200px' }}>
                    {content}
                    <div className="flex mt-2 justify-content-between">
                        <Button
                            type="button"
                            label="Clear"
                            className="p-button-sm p-button-text"
                            onClick={() => {
                                setLocalInputValue(isMulti ? [] : "");
                                clearFilter(fieldsToClear);
                                op.current.hide();
                            }}
                        />
                        {applyButtonNeeded && (
                            <Button
                                type="button"
                                label="Apply"
                                className="p-button-sm"
                                onClick={() => {
                                    updateFilter(filterKey, localInputValue);
                                    op.current.hide();
                                }}
                            />
                        )}
                    </div>
                </div>
            </OverlayPanel>
        </div>
    );
};

export default FilterButton;