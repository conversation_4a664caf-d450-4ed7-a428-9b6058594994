// AuthProvider.jsx
import { PublicClientApplication } from "@azure/msal-browser";
import { MsalProvider } from "@azure/msal-react";
import { msalApp } from "./msalInstance";

export const AuthProvider = ({ children }) => {
  //const pca = useMemo(() => {
    /*if (typeof window !== "undefined" && window.crypto?.subtle) {
      return new PublicClientApplication(msalConfig);
    } else {
      console.error("MSAL requires a secure context (https or localhost) with window.crypto.subtle available.");
      return null;
    }
  }, []);

  if (!pca) return null;*/

  if (!msalApp) {
    return <div>MSAL Initialization Failed. Please ensure your application is running in a secure context (HTTPS or localhost) and that browser crypto functions are available.</div>;
  }

  //return <MsalProvider instance={pca}>{children}</MsalProvider>;
  return <MsalProvider instance={msalApp}>{children}</MsalProvider>;
};
