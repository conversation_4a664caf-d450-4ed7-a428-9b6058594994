import React, { useState } from 'react';
import { Card } from 'primereact/card';
import { Steps } from 'primereact/steps';
import { Panel } from 'primereact/panel';
import { Badge } from 'primereact/badge';
import { Message } from 'primereact/message';
import { TabView, TabPanel } from 'primereact/tabview';
import SystemArchitectureDiagram from './SystemArchitecture';

const GettingStarted = () => {
  const [activeStep, setActiveStep] = useState(0);

  const workflowSteps = [
    {
      label: 'OCR Scanning',
      icon: 'pi pi-eye'
    },
    {
      label: 'Data Matching',
      icon: 'pi pi-sync'
    },
    {
      label: 'User Review',
      icon: 'pi pi-user'
    },
    {
      label: 'RightAngle Processing',
      icon: 'pi pi-send'
    }
  ];

  const stepDetails = [
    {
      title: "OCR Scanning",
      description: "Automated document processing and data extraction",
      content: [
        {
          header: "Email Processing",
          details: [
            "Monitors email inbox for new invoices and extracts attachments",
            "Splits attachments into individual documents for processing",
            "Reads PDF files"
          ]
        },
        {
          header: "Microsoft Forms OCR",
          details: [
            "Advanced optical character recognition",
            "Pulls key fields from trained dataset",
            "Data validation and formatting",
            "Confidence scoring for extracted data"
          ]
        },
        {
          header: "Data Storage",
          details: [
            "Secure storage in Databricks staging tables",
            "Original document preservation",
            "Extracted data normalization"
          ]
        }
      ]
    },
    {
      title: "Data Matching",
      description: "Intelligent matching with existing RightAngle data",
      content: [
        {
          header: "RightAngle Data",
          details: [
            "Synchronizes staged data with RightAngle reference data",
            "Matches text values to RightAngle",
            "Duplicate invoice detection"
          ]
        },
        {
          header: "Cross Reference Data",
          details: [
            "Matches non exact records through cross reference tool",
            "Internal cross reference tool for AP Automation application",
            "Cross Reference data available to add update and edit",
            "Categories available for all incoming text categories"
          ]
        },
        {
          header: "Detail Matching",
          details: [
            "Runs matching scripts on existing RightAngle records",
            "Fills in full data of detail records to matched record",
            "Visual indicators for matched detail records"
          ]
        }
      ]
    },
    {
      title: "User Review",
      description: "Manual validation and correction of processed data",
      content: [
        {
          header: "Data Validation",
          details: [
            "Review automatically matched vendor information",
            "Verify extracted amounts and dates",
            "Ensure detail records are matching",
            "Check for duplicate invoices"
          ]
        },
        {
          header: "Manual Corrections",
          details: [
            "Edit mismatched or incorrect data",
            "Assign proper data for unmatched items",
            "Add missing cross reference information",
            "Flag invoices requiring investigation"
          ]
        },
        {
          header: "Approval Process",
          details: [
            "Set records to Process when prepared",
            "Add comments and notes for audit trail",
            "Use Override for difficult records",
            "Check for errors in processing status"
          ]
        }
      ]
    },
    {
      title: "RightAngle Processing",
      description: "Final integration and payment processing",
      content: [
        {
          header: "Data Transfer",
          details: [
            "Transfer using RightAngle task",
            "Data validation during process",
            "Errors sent back to dashboard",
            "Processing status tracking"
          ]
        },
        {
          header: "Invoice Records",
          details: [
            "Payable creation in RightAngle system",
            "Detail records created",
            "Details matched to accounting records when able",
            "Ready for SAP processing"
          ]
        },
        {
          header: "User Notes",
          details: [
            "Verify payables creation and data",
            "Ensure detail match status",
            "Move payable to feed status if applicable"
          ]
        }
      ]
    }
  ];

  const userTasks = [
    {
      title: 'Monitor Incoming Records',
      description: 'Check the dashboard regularly for new invoice records that require attention.',
      icon: 'pi pi-eye'
    },
    {
      title: 'Review Auto-Matched Data',
      description: 'Verify that automatically matched records contain correct counterparty, amount, volume, transaction date etc. .',
      icon: 'pi pi-check'
    },
    {
      title: 'Handle Unmatched Records',
      description: 'Manually match records that the system could not automatically process or flag for investigation.',
      icon: 'pi pi-exclamation-triangle'
    },
    {
      title: 'Process Approved Records',
      description: 'Submit verified records to RightAngle for final processing and payment.',
      icon: 'pi pi-arrow-right'
    }
  ];

  const headerTemplate = (
    <div className="bg-primary text-primary-50 p-4 border-round">
      <h1 className="text-3xl font-bold m-0 mb-2">AP Invoice Automation - User Manual</h1>
      <p className="text-lg m-0 text-primary-100">
        System overview and user responsibilities for invoice processing workflow.
      </p>
    </div>
  );

  return (
    <div className="flex flex-column gap-4">
      {/* Header Section */}
      <Card className="shadow-2">
        {headerTemplate}
      </Card>

      {/* System Overview */}
      <Card title="System Overview" className="shadow-2">
        <Message 
          severity="info" 
          text="This system processes AP invoices from email intake through RightAngle integration. Click on any step below to view detailed information."
          className="mb-4"
        />
        
        <p className="text-700 mb-4">
          The AP Invoice Automation system handles the technical processing of invoice documents. 
          As a functional user, you are responsible for reviewing the results and ensuring data accuracy before final processing.
        </p>

        <div className="mb-4">
          <Steps model={workflowSteps} activeIndex={activeStep} onSelect={(e) => setActiveStep(e.index)} readOnly={false} />
        </div>

        {/* Dynamic Step Details */}
        <div className="mt-4">
          <div className="mb-3">
            <h3 className="text-primary m-0 mb-2">{stepDetails[activeStep].title}</h3>
            <p className="text-600 m-0">{stepDetails[activeStep].description}</p>
          </div>
          
          <div className="grid">
            {stepDetails[activeStep].content.map((section, index) => (
              <div key={index} className="col-12 md:col-4">
                <Panel header={section.header} className="h-full">
                  <ul className="text-600 text-sm pl-3 m-0">
                    {section.details.map((detail, detailIndex) => (
                      <li key={detailIndex} className="mb-1">{detail}</li>
                    ))}
                  </ul>
                </Panel>
              </div>
            ))}
          </div>
        </div>
      </Card>

      {/* User Responsibilities */}
      <Card title="Daily User Tasks" className="shadow-2">
        <p className="text-600 mb-4">
          The following tasks require regular user attention to maintain efficient invoice processing:
        </p>
        
        <div className="grid">
          {userTasks.map((task, index) => (
            <div key={index} className="col-12 md:col-6">
              <div className="surface-100 border-round p-4 h-full border-left-3 border-primary">
                <div className="flex align-items-start">
                  <Badge value={index + 1} severity="info" className="mr-3" />
                  <div className="flex-1">
                    <div className="flex align-items-center mb-2">
                      <i className={`${task.icon} text-lg text-primary mr-2`}></i>
                      <h4 className="m-0">{task.title}</h4>
                    </div>
                    <p className="text-600 m-0 text-sm">{task.description}</p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Data Sources */}
      <Card title="Data Sources & Integration" className="shadow-2">
        <div className="grid">
          <div className="col-12 md:col-6">
            <h4 className="text-primary mb-3">Input Sources</h4>
            
            <div className="flex align-items-start mb-3">
              <i className="pi pi-envelope text-lg text-blue-500 mr-3 mt-1"></i>
              <div>
                <h5 className="m-0 mb-1">Email Invoices</h5>
                <p className="text-600 m-0 text-sm">
                  PDF and image attachments processed via Microsoft Forms OCR
                </p>
              </div>
            </div>

            <div className="flex align-items-start mb-3">
              <i className="pi pi-database text-lg text-green-500 mr-3 mt-1"></i>
              <div>
                <h5 className="m-0 mb-1">Databricks Staging</h5>
                <p className="text-600 m-0 text-sm">
                  Extracted invoice data stored in staging tables for processing
                </p>
              </div>
            </div>
          </div>

          <div className="col-12 md:col-6">
            <h4 className="text-primary mb-3">Integration Points</h4>
            
            <div className="flex align-items-start mb-3">
              <i className="pi pi-sync text-lg text-purple-500 mr-3 mt-1"></i>
              <div>
                <h5 className="m-0 mb-1">RightAngle Data</h5>
                <p className="text-600 m-0 text-sm">
                  BusinessAssociate, Product, Location data from RightAngle system
                </p>
              </div>
            </div>

            <div className="flex align-items-start mb-3">
              <i className="pi pi-arrow-right text-lg text-teal-500 mr-3 mt-1"></i>
              <div>
                <h5 className="m-0 mb-1">RightAngle Processing</h5>
                <p className="text-600 m-0 text-sm">
                  Final destination for approved payable records
                </p>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Important Notes */}
      <Card title="Important Notes" className="shadow-2">
        <div className="grid">
          <div className="col-12 md:col-6">
            <Message 
              severity="warn" 
              text="Always verify counterparty information before processing to avoid payment errors."
              className="mb-3"
            />
            <Message 
              severity="info" 
              text="Records with low confidence matching scores will need manual data entry."
              className="mb-3"
            />
          </div>
          <div className="col-12 md:col-6">
            <Message 
              severity="error" 
              text="Do not process duplicate invoices - check for existing records first."
              className="mb-3"
            />
            <Message 
              severity="success" 
              text="Use the audit trail to track processing history for each record."
              className="mb-3"
            />
          </div>
        </div>
      </Card>
    </div>
  );
};

export default GettingStarted;