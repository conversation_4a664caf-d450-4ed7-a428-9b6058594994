import React, { useState } from 'react';
import { Card } from 'primereact/card';
import { Panel } from 'primereact/panel';
import { Badge } from 'primereact/badge';
import { Message } from 'primereact/message';
import { Divider } from 'primereact/divider';
import { Dropdown } from 'primereact/dropdown';

const FunctionsHelp = () => {
  const [selectedStatus, setSelectedStatus] = useState('Staged');

  // Button component that matches the toolbar style exactly
  const ActionButton = ({ label, disabled = false, loading = false }) => {
    const isDisabled = disabled || loading;
    return (
      <button
        type="button"
        disabled={isDisabled}
        className="p-button p-button-raised"
        style={{
          border: '1px solid black',
          backgroundColor: 'white',
          color: 'black',
          opacity: isDisabled ? 0.5 : 1,
          pointerEvents: isDisabled ? 'none' : 'auto',
          cursor: isDisabled ? 'not-allowed' : 'pointer',
          marginRight: '8px'
        }}
      >
        {loading ? <i className="pi pi-spin pi-spinner" style={{ fontSize: '1rem' }}></i> : label}
      </button>
    );
  };

  // Status toggle component that matches the toolbar
  const StatusToggle = ({ currentStatus }) => (
    <div className="status-toggle-container" style={{ display: 'flex', border: '2px solid #007bff', borderRadius: '8px', overflow: 'hidden' }}>
      {["Pending", "Processed"].map(statusValue => (
        <div
          key={statusValue}
          style={{
            padding: '10px 20px',
            cursor: 'pointer',
            backgroundColor: currentStatus === statusValue ? '#007bff' : 'transparent',
            color: currentStatus === statusValue ? 'white' : '#007bff',
            fontWeight: currentStatus === statusValue ? 'bold' : 'normal',
          }}
        >
          {statusValue}
        </div>
      ))}
    </div>
  );

  const getButtonConfig = (status) => {
    switch (status) {
      case "Staged":
        return {
          edit: { disabled: false, label: "Edit" },
          process: { disabled: false, label: "Process" },
          override: { disabled: false, label: "Override" },
          ignore: { disabled: false, label: "Ignore" }
        };
      case "Processing":
        return {
          edit: { disabled: true, label: "Edit" },
          process: { disabled: false, label: "Cancel" },
          override: { disabled: true, label: "Override" },
          ignore: { disabled: true, label: "Ignore" }
        };
      case "Processed":
      case "Override":
        return {
          edit: { disabled: false, label: "Edit" },
          process: { disabled: false, label: "Reprocess" },
          override: { disabled: true, label: "Override" },
          ignore: { disabled: true, label: "Ignore" }
        };
      case "Ignored":
        return {
          edit: { disabled: true, label: "Edit" },
          process: { disabled: true, label: "Process" },
          override: { disabled: true, label: "Override" },
          ignore: { disabled: false, label: "UnIgnore" }
        };
      case "Completed":
        return {
          edit: { disabled: true, label: "Edit" },
          process: { disabled: true, label: "Process" },
          override: { disabled: true, label: "Override" },
          ignore: { disabled: true, label: "Ignore" }
        };
      default:
        return {};
    }
  };

  const toolbarButtons = [
    {
      title: 'Edit',
      icon: 'pi pi-pencil',
      description: 'Modify the selected invoice record data and details.',
      usage: 'Click to open the edit dialog for the selected invoice record.',
      statusBehavior: {
        'Staged': 'Available - Edit invoice data',
        'Processing': 'Disabled - Cannot edit while processing',
        'Processed': 'Available - Edit processed record',
        'Override': 'Available - Edit overridden record',
        'Ignored': 'Disabled - Cannot edit ignored records',
        'Completed': 'Disabled - Cannot edit completed records'
      },
      requirements: ['One invoice record must be selected']
    },
    {
      title: 'Process',
      icon: 'pi pi-play',
      description: 'Process the selected invoice through the workflow or cancel current processing.',
      usage: 'Button label and function changes based on current status.',
      statusBehavior: {
        'Staged': 'Process - Submit invoice for processing',
        'Processing': 'Cancel - Stop current processing',
        'Processed': 'Reprocess - Process the record again',
        'Override': 'Reprocess - Process the overridden record again',
        'Ignored': 'Disabled - Cannot process ignored records',
        'Completed': 'Disabled - Cannot reprocess completed records'
      },
      requirements: ['One invoice record must be selected']
    },
    {
      title: 'Reprocess',
      icon: 'pi pi-refresh',
      description: 'Re-run processing on records that have already been processed or overridden.',
      usage: 'Available only for Processed and Override status records. The Process button changes to "Reprocess" for these statuses.',
      statusBehavior: {
        'Staged': 'Not Available - Use Process instead',
        'Processing': 'Not Available - Cancel processing first',
        'Processed': 'Available - Reprocess the completed record',
        'Override': 'Available - Reprocess the overridden record',
        'Ignored': 'Not Available - UnIgnore first, then process',
        'Completed': 'Not Available - Cannot reprocess completed records'
      },
      requirements: ['One invoice record must be selected', 'Record must be in Processed or Override status'],
      notes: 'Reprocessing will clear previous processing results and run the workflow again. This action cannot be undone.'
    },
    {
      title: 'Override',
      icon: 'pi pi-shield',
      description: 'Override normal processing rules for the selected invoice.',
      usage: 'Use when invoice needs to bypass standard processing workflow.',
      statusBehavior: {
        'Staged': 'Available - Override processing rules',
        'Processing': 'Disabled - Cannot override while processing',
        'Processed': 'Disabled - Already processed',
        'Override': 'Disabled - Already overridden',
        'Ignored': 'Disabled - Cannot override ignored records',
        'Completed': 'Disabled - Cannot override completed records'
      },
      requirements: ['One invoice record must be selected', 'Record must be in Staged status']
    },
    {
      title: 'Ignore/Unignore',
      icon: 'pi pi-ban',
      description: 'Mark invoice to be ignored in processing or restore ignored invoice.',
      usage: 'Button changes to "UnIgnore" when record is already ignored.',
      statusBehavior: {
        'Staged': 'Ignore - Mark record to be ignored',
        'Processing': 'Disabled - Cannot ignore while processing',
        'Processed': 'Disabled - Cannot ignore processed records',
        'Override': 'Disabled - Cannot ignore overridden records',
        'Ignored': 'UnIgnore - Restore ignored record to Staged',
        'Completed': 'Disabled - Cannot ignore completed records'
      },
      requirements: ['One invoice record must be selected']
    },
    {
      title: 'Notes',
      icon: 'pi pi-comment',
      description: 'Add, view, or edit notes associated with the selected invoice.',
      usage: 'Opens notes dialog to manage comments and annotations for the record.',
      statusBehavior: {
        'All Statuses': 'Available - Manage notes for selected record'
      },
      requirements: ['One invoice record must be selected']
    },
    {
      title: 'Logs',
      icon: 'pi pi-list',
      description: 'View processing logs and audit trail for the selected invoice.',
      usage: 'Opens log viewer showing processing history and system actions.',
      statusBehavior: {
        'All Statuses': 'Available - View processing logs'
      },
      requirements: ['One invoice record must be selected']
    },
    {
      title: 'Files',
      icon: 'pi pi-file',
      description: 'Access PDF files and attachments associated with the selected invoice.',
      usage: 'Opens file viewer to display original invoice documents and attachments.',
      statusBehavior: {
        'All Statuses': 'Available - View associated files'
      },
      requirements: ['One invoice record must be selected']
    }
  ];

  const utilityFunctions = [
    {
      title: 'Export CSV',
      icon: 'pi pi-download',
      description: 'Export all invoice data to a CSV file for external analysis or reporting.',
      usage: 'Downloads a CSV file containing all invoice records and their details.',
      requirements: ['At least one invoice record must be in the system'],
      notes: 'Export includes both invoice headers and detail line items.'
    },
    {
      title: 'Status Filter',
      icon: 'pi pi-filter',
      description: 'Toggle between viewing Pending and Processed invoice records.',
      usage: 'Click Pending or Processed to filter the invoice list display.',
      options: [
        'Pending - Shows Staged, Processing, and other non-final statuses',
        'Processed - Shows Processed, Override, Ignored, and Completed records'
      ],
      notes: 'Filter affects which records are displayed in the main invoice table.'
    }
  ];

  const statusDefinitions = [
    { status: 'Staged', description: 'Invoice data extracted and ready for processing', color: 'info' },
    { status: 'Processing', description: 'Invoice currently being processed by the system', color: 'warning' },
    { status: 'Processed', description: 'Invoice successfully processed through normal workflow', color: 'success' },
    { status: 'Override', description: 'Invoice processed with override rules applied', color: 'secondary' },
    { status: 'Ignored', description: 'Invoice marked to be excluded from processing', color: 'danger' },
    { status: 'Completed', description: 'Invoice processing fully complete, no further actions allowed', color: 'success' }
  ];

  const statusOptions = statusDefinitions.map(s => ({ label: s.status, value: s.status }));
  const currentButtonConfig = getButtonConfig(selectedStatus);

  return (
    <div className="flex flex-column gap-4">
      {/* Header */}
      <Card className="shadow-2">
        <div className="bg-primary text-primary-50 p-4 border-round">
          <h1 className="text-3xl font-bold m-0 mb-2">Dashboard Functions</h1>
          <p className="text-lg m-0 text-primary-100">
            Invoice toolbar functions and their behavior based on record status
          </p>
        </div>
      </Card>

      {/* Interactive Button Demo */}
      <Card title="Interactive Button Demo" className="shadow-2">
        <div className="mb-4">
          <label className="block font-medium mb-2">Select a status to see button behavior:</label>
          <Dropdown 
            value={selectedStatus} 
            options={statusOptions} 
            onChange={(e) => setSelectedStatus(e.value)}
            className="w-12rem"
          />
        </div>
        
        <div className="p-4 surface-100 border-round">
          <div className="flex justify-content-between align-items-center">
            <div className="flex gap-2 flex-wrap">
              <ActionButton label={currentButtonConfig.edit?.label || "Edit"} disabled={currentButtonConfig.edit?.disabled} />
              <ActionButton label={currentButtonConfig.process?.label || "Process"} disabled={currentButtonConfig.process?.disabled} />
              <ActionButton label={currentButtonConfig.override?.label || "Override"} disabled={currentButtonConfig.override?.disabled} />
              <ActionButton label={currentButtonConfig.ignore?.label || "Ignore"} disabled={currentButtonConfig.ignore?.disabled} />
              <ActionButton label="Notes" />
              <ActionButton label="Logs" />
              <ActionButton label="Files" />
            </div>
            
            <div className="flex gap-2 align-items-center">
              <ActionButton label="Export CSV" />
              <StatusToggle currentStatus="Pending" />
            </div>
          </div>
        </div>
        
        <Message 
          severity="info" 
          text={`Current Status: ${selectedStatus} - Buttons shown above reflect their actual availability and labels for this status.`}
          className="mt-3"
        />
      </Card>

      {/* Toolbar Functions Overview */}
      <Card title="Toolbar Functions Overview" className="shadow-2">
        <Message 
          severity="info" 
          text="All toolbar functions require selecting an invoice record first. Button availability depends on the record's processing status."
          className="mb-4"
        />
        
        <div className="grid">
          {toolbarButtons.map((func, index) => (
            <div key={index} className="col-12">
              <Panel 
                header={
                  <div className="flex align-items-center">
                    <i className={`${func.icon} text-primary mr-2`}></i>
                    <span className="font-semibold mr-3">{func.title}</span>
                    <ActionButton label={func.title} />
                  </div>
                } 
                className="mb-3"
                toggleable
              >
                <div className="grid">
                  <div className="col-12 md:col-8">
                    <p className="text-700 mb-3">{func.description}</p>
                    
                    <div className="mb-3">
                      <h5 className="text-primary mb-2">Usage:</h5>
                      <p className="text-600 text-sm m-0">{func.usage}</p>
                    </div>

                    <div className="mb-3">
                      <h5 className="text-primary mb-2">Requirements:</h5>
                      <ul className="text-600 text-sm pl-3 m-0">
                        {func.requirements.map((req, reqIndex) => (
                          <li key={reqIndex}>{req}</li>
                        ))}
                      </ul>
                    </div>

                    {func.notes && (
                      <div className="surface-200 border-round p-3 mt-3">
                        <Message 
                          severity="warn" 
                          text={`Important: ${func.notes}`}
                        />
                      </div>
                    )}
                  </div>
                  
                  <div className="col-12 md:col-4">
                    <div className="surface-100 border-round p-3">
                      <h5 className="text-primary mb-2">Status Behavior</h5>
                      <div className="flex flex-column gap-1">
                        {Object.entries(func.statusBehavior).map(([status, behavior], behaviorIndex) => (
                          <div key={behaviorIndex} className="text-sm">
                            <Badge value={status} severity="secondary" className="mr-2" />
                            <span className="text-600">{behavior}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </Panel>
            </div>
          ))}
        </div>
      </Card>

      {/* Utility Functions */}
      <Card title="Utility Functions" className="shadow-2">
        <p className="text-600 mb-4">
          Additional functions available from the toolbar for data management and filtering.
        </p>
        
        <div className="grid">
          {utilityFunctions.map((func, index) => (
            <div key={index} className="col-12 md:col-6">
              <div className="surface-50 border-round p-4 h-full border-left-3 border-blue-500">
                <div className="flex align-items-center mb-3">
                  <i className={`${func.icon} text-lg text-blue-500 mr-2`}></i>
                  <h4 className="m-0 mr-3">{func.title}</h4>
                  {func.title === 'Export CSV' && <ActionButton label="Export CSV" />}
                  {func.title === 'Status Filter' && <StatusToggle currentStatus="Pending" />}
                </div>
                
                <p className="text-600 text-sm mb-3">{func.description}</p>
                
                <div className="mb-3">
                  <h5 className="text-primary text-sm mb-2">Usage:</h5>
                  <p className="text-600 text-sm m-0">{func.usage}</p>
                </div>

                {func.requirements && (
                  <div className="mb-3">
                    <h5 className="text-primary text-sm mb-2">Requirements:</h5>
                    <ul className="text-600 text-sm pl-3 m-0">
                      {func.requirements.map((req, reqIndex) => (
                        <li key={reqIndex}>{req}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {func.options && (
                  <div className="mb-3">
                    <h5 className="text-primary text-sm mb-2">Options:</h5>
                    <ul className="text-600 text-sm pl-3 m-0">
                      {func.options.map((option, optIndex) => (
                        <li key={optIndex}>{option}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {func.notes && (
                  <div className="surface-100 border-round p-2 mt-3">
                    <p className="text-600 text-sm m-0"><strong>Note:</strong> {func.notes}</p>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Processing Status Reference */}
      <Card title="Processing Status Reference" className="shadow-2">
        <p className="text-600 mb-4">
          Understanding invoice processing statuses and their impact on available functions:
        </p>
        
        <div className="grid">
          {statusDefinitions.map((statusDef, index) => (
            <div key={index} className="col-12 md:col-6 lg:col-4">
              <div className="surface-100 border-round p-3 mb-3">
                <div className="flex align-items-center mb-2">
                  <Badge value={statusDef.status} severity={statusDef.color} className="mr-2" />
                </div>
                <p className="text-600 text-sm m-0">{statusDef.description}</p>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Function Usage Guidelines */}
      <Card title="Function Usage Guidelines" className="shadow-2">
        <div className="grid">
          <div className="col-12 md:col-6">
            <h4 className="text-green-600 mb-3">Best Practices</h4>
            <ul className="text-600 text-sm pl-3 m-0">
              <li>Always select a record before attempting to use toolbar functions</li>
              <li>Check the processing status before performing actions</li>
              <li>Use Notes function to document any manual interventions</li>
              <li>Review Logs when troubleshooting processing issues</li>
              <li>Export data regularly for backup and reporting</li>
              <li>Use Override function sparingly and document reasons</li>
              <li>When reprocessing, ensure you understand why the record needs to be processed again</li>
            </ul>
          </div>
          <div className="col-12 md:col-6">
            <h4 className="text-orange-600 mb-3">Important Notes</h4>
            <ul className="text-600 text-sm pl-3 m-0">
              <li>Processing actions cannot be undone - verify before proceeding</li>
              <li>Ignored records require UnIgnore before they can be processed</li>
              <li>Completed records cannot be modified or reprocessed</li>
              <li>Files function shows original invoice attachments</li>
              <li>Status filter affects which records are visible in the table</li>
              <li>Export includes all records regardless of current filter</li>
              <li>Reprocessing will clear previous processing results and start fresh</li>
            </ul>
          </div>
        </div>
        
        <Divider />
        
        <Message 
          severity="warn" 
          text="Button availability changes dynamically based on selected record status. Gray/disabled buttons indicate the function is not available for the current record."
        />
      </Card>
    </div>
  );
};

export default FunctionsHelp;