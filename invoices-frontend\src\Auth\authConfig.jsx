// authConfig.js
const environments = {
  __ENVIRONMENT__: {
    clientId: "6bf39fe4-1bcf-4697-9b44-8c294ad31dc4",
    authority: "https://login.microsoftonline.com/a530807a-40d9-47ea-ad58-8e093f2f9f49"
  },
  development: {
    clientId: "6bf39fe4-1bcf-4697-9b44-8c294ad31dc4",
    authority: "https://login.microsoftonline.com/a530807a-40d9-47ea-ad58-8e093f2f9f49"
  },
  test: {
    clientId: "",
    authority: ""
  },
  production: {
    clientId: "",
    authority: ""
  }
}

const currentEnv = window.__APP_ENV__  || 'development';
const config = environments[currentEnv];

if (!config) {
    throw new Error(`No configuration found for environment: ${currentEnv}`);
  }

export const msalConfig = {
  auth: {
    clientId: config.clientId,
    authority: config.authority,
    redirectUri: window.location.origin,
  },
  cache: {
    cacheLocation: "sessionStorage", // This configures where your cache will be stored
    storeAuthStateInCookie: false, // Set this to "true" if you are having issues on IE11 or Edge
  }
};

export const loginRequest = {
  scopes: ["User.Read"]
};

export const protectedResources = {
  graphMe: {
    endpoint: "https://graph.microsoft.com/v1.0/me",
    scopes: ["User.Read"],
  }
};