// Storage key
const STORAGE_KEY = 'invoice-dashboard-state';

// Initial state factory
export const getInitialFilters = () => {
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
    
    return {
        counterparty: null,
        invoiceDateFrom: thirtyDaysAgo,
        invoiceDateTo: today,
        dueDateFrom: null,
        dueDateTo: null,
    };
};

const initialState = {
    // Filters and UI state
    filters: getInitialFilters(),
    statusFilter: "Pending",
    selectedId: null,
    
    // Data cache
    invoices: [],
    details: {},
    allBaOptions: [],
    
    // Loading states
    loading: false,
    lastFetchTime: null,
    
    // Settings - key change here
    filtersInitialized: false,
    isFirstLoad: true, // Add this to track if it's the very first load
};

// Load state from sessionStorage
export const loadPersistedState = () => {
    try {
        const stored = sessionStorage.getItem(STORAGE_KEY);
        if (stored) {
            const parsed = JSON.parse(stored);
            
            // Convert date strings back to Date objects - but only if they're not null
            if (parsed.filters) {
                ['invoiceDateFrom', 'invoiceDateTo', 'dueDateFrom', 'dueDateTo'].forEach(field => {
                    if (parsed.filters[field] && parsed.filters[field] !== null) {
                        parsed.filters[field] = new Date(parsed.filters[field]);
                    } else {
                        // Explicitly set to null if it was null
                        parsed.filters[field] = null;
                    }
                });
            }
            
            if (parsed.lastFetchTime && parsed.lastFetchTime !== null) {
                parsed.lastFetchTime = new Date(parsed.lastFetchTime);
            } else {
                parsed.lastFetchTime = null;
            }
            
            // If we have persisted state, mark as initialized and not first load
            const restoredState = { 
                ...initialState, 
                ...parsed,
                filtersInitialized: true, // Mark as initialized since we loaded from storage
                isFirstLoad: false // Not the first load anymore
            };
            
            return restoredState;
        }
    } catch (error) {
        console.warn('Failed to load persisted state:', error);
    }
    return initialState;
};

// Save state to sessionStorage
export const saveState = (state) => {
    try {
        // Don't persist loading state or isFirstLoad
        const stateToSave = {
            ...state,
            loading: false,
            isFirstLoad: false, // Never persist this
        };
        
        // Ensure null values for date fields are properly preserved
        if (stateToSave.filters) {
            const cleanedFilters = { ...stateToSave.filters };
            ['invoiceDateFrom', 'invoiceDateTo', 'dueDateFrom', 'dueDateTo'].forEach(field => {
                if (cleanedFilters[field] === null || cleanedFilters[field] === undefined) {
                    cleanedFilters[field] = null;
                }
            });
            stateToSave.filters = cleanedFilters;
        }
        
        sessionStorage.setItem(STORAGE_KEY, JSON.stringify(stateToSave));
    } catch (error) {
        console.warn('Failed to save state:', error);
    }
};

// Reducer
export const invoiceReducer = (state, action) => {
    let newState;
    
    switch (action.type) {
        case 'SET_FILTERS':
            newState = { ...state, filters: action.payload };
            break;
            
        case 'UPDATE_FILTER':
            newState = { 
                ...state, 
                filters: { ...state.filters, [action.field]: action.value }
            };
            break;
            
        case 'RESET_FILTERS':
            newState = { ...state, filters: getInitialFilters() };
            break;
            
        case 'SET_STATUS_FILTER':
            newState = { 
                ...state, 
                statusFilter: action.payload,
                // Clear selection when changing status filter
                selectedId: null
            };
            break;
            
        case 'SET_SELECTED_ID':
            newState = { ...state, selectedId: action.payload };
            break;
            
        case 'SET_LOADING':
            newState = { ...state, loading: action.payload };
            break;
            
        case 'SET_DATA':
            newState = { 
                ...state, 
                invoices: action.payload.invoices || [],
                details: action.payload.details || {},
                allBaOptions: action.payload.allBaOptions || state.allBaOptions || [],
                lastFetchTime: new Date(),
                loading: false
            };
            break;
            
        case 'SET_FILTERS_INITIALIZED':
            newState = { 
                ...state, 
                filtersInitialized: action.payload,
                isFirstLoad: false // Once initialized, it's no longer first load
            };
            break;
            
        case 'CLEAR_CACHE':
            newState = { 
                ...state, 
                invoices: [],
                details: {},
                lastFetchTime: null 
            };
            break;
            
        default:
            return state;
    }
    
    // Save to sessionStorage after each state change
    saveState(newState);
    return newState;
};

// Utility function to clear cache (for use in hooks)
export const clearCacheFromStorage = () => {
    sessionStorage.removeItem(STORAGE_KEY);
};