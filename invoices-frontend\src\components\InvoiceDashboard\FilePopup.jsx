import React, { useState, useEffect, useRef, useCallback } from "react";
import { Dialog } from "primereact/dialog";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { Button } from "primereact/button";
import { FileUpload } from "primereact/fileupload";
import { Toast } from "primereact/toast";
import "primereact/resources/themes/lara-light-indigo/theme.css";
import "primereact/resources/primereact.min.css";
import apiService from "../api/apiService";
import { useApi } from "../api/useApi";
import { useAuth } from "../../Auth/useAuth";

const FilePopup = ({ visible, onHide, headerID }) => {
  const [attachments, setAttachments] = useState([]);
  const { getAuthHeaders } = useAuth();
  const { loading: attachmentsLoading, callApi: callAttachmentsApi } = useApi();
  const { loading: uploadLoading, callApi: callUploadApi } = useApi();
  const { loading: downloadLoading, callApi: callDownloadApi } = useApi();
  const toast = useRef(null);
  const fileUploadRef = useRef(null);

  const fetchAttachments = useCallback(async () => {
    try {
      const response = await callAttachmentsApi(() => 
        apiService.get('/Attachments/GetAttachments', { headerId: headerID }, getAuthHeaders)
      );
      setAttachments(response || []);
    } catch (err) {
      console.error("Error fetching attachments: ", err);
      toast.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to load attachments'
      });
    }
  }, [headerID, callAttachmentsApi, getAuthHeaders]);

  useEffect(() => {
    if (visible && headerID) {
      fetchAttachments();
    }
  }, [visible, headerID, fetchAttachments]);

  // This is the key change - handle the custom upload
  const handleCustomUpload = async (event) => {
    const files = event.files;
    if (!files || files.length === 0) return;

    try {
      const formData = new FormData();
      formData.append('headerId', headerID);
      
      // Add each file to the form data
      files.forEach((file) => {
        formData.append('Files', file);
      });

      await callUploadApi(() => 
        apiService.postFormData('/Attachments/UploadAttachment', formData, getAuthHeaders)
      );

      toast.current?.show({
        severity: 'success',
        summary: 'Success',
        detail: `${files.length} file(s) uploaded successfully`
      });

      // Clear the file upload component
      fileUploadRef.current?.clear();
      
      // Refresh attachments list
      await fetchAttachments();
    } catch (err) {
      console.error("Error uploading files: ", err);
      toast.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to upload files'
      });
    }
  };

    const handleDownload = async (attachment) => {
        try {
            const response = await callDownloadApi(() => 
            apiService.postDownload('/Attachments/DownloadAttachment', 
                { attachmentId: attachment.id }, 
                getAuthHeaders
            )
            );

            // Create blob and download
            const blob = new Blob([response], { type: attachment.mimeType || 'application/octet-stream' });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = attachment.fileName || 'download';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);

            toast.current?.show({
            severity: 'success',
            summary: 'Success',
            detail: 'File downloaded successfully'
            });
        } catch (err) {
            console.error("Error downloading file: ", err);
            toast.current?.show({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to download file'
            });
        }
        };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    
    try {
      const date = new Date(dateString);
      return isNaN(date.getTime()) 
        ? 'Invalid Date' 
        : date.toLocaleString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          });
    } catch (e) {
      console.error('Error formatting date:', e);
      return 'Invalid Date';
    }
  };

  // Format file size for display
  const formatFileSize = (bytes) => {
    if (!bytes || bytes === 0) return 'N/A';
    
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  // Download button template
  const downloadActionTemplate = (rowData) => {
    return (
      <Button
        icon="pi pi-download"
        className="p-button-rounded p-button-text"
        onClick={() => handleDownload(rowData)}
        loading={downloadLoading}
        tooltip="Download file"
        tooltipOptions={{ position: 'top' }}
      />
    );
  };

  // Custom upload header template
  const uploadHeaderTemplate = (options) => {
    const { className, chooseButton, uploadButton, cancelButton } = options;
    
    return (
      <div className={className} style={{ backgroundColor: 'transparent', display: 'flex', alignItems: 'center' }}>
        {chooseButton}
        {uploadButton}
        {cancelButton}
      </div>
    );
  };

  return (
    <>
      <Toast ref={toast} />
      <Dialog
        header="PDF Attachments"
        visible={visible}
        onHide={onHide}
        style={{ width: "60vw" }}
        breakpoints={{ "960px": "75vw", "641px": "90vw" }}
      >
        <div className="flex flex-column gap-4">
          {/* File Upload Section */}
          <div className="flex flex-column gap-2">
            <h4>Upload New Files</h4>
            <FileUpload
              ref={fileUploadRef}
              mode="advanced"
              multiple
              accept=".pdf,.doc,.docx,.xls,.xlsx,.png,.jpg,.jpeg"
              maxFileSize={10000000} // 10MB
              customUpload={true}
              uploadHandler={handleCustomUpload} // Changed from onUpload to uploadHandler
              auto={false}
              headerTemplate={uploadHeaderTemplate}
              emptyTemplate={
                <p className="m-0">Drag and drop files here or click to browse.</p>
              }
              chooseOptions={{
                icon: 'pi pi-fw pi-plus',
                iconOnly: false,
                className: 'custom-choose-btn p-button-rounded p-button-outlined'
              }}
              uploadOptions={{
                icon: 'pi pi-fw pi-cloud-upload',
                iconOnly: false,
                className: 'custom-upload-btn p-button-success p-button-rounded p-button-outlined',
                disabled: uploadLoading
              }}
              cancelOptions={{
                icon: 'pi pi-fw pi-times',
                iconOnly: false,
                className: 'custom-cancel-btn p-button-danger p-button-rounded p-button-outlined'
              }}
            />
          </div>

          {/* Attachments List */}
          <div className="mt-3">
            <h4>Attached Files</h4>
            <DataTable
              value={attachments}
              loading={attachmentsLoading}
              scrollable
              scrollHeight="400px"
              emptyMessage={attachmentsLoading ? "Loading attachments..." : "No files attached"}
            >
              <Column 
                field="fileName"
                header="File Name" 
                body={(rowData) => (
                  <div className="flex align-items-center gap-2">
                    <i className="pi pi-file" style={{ color: '#6c757d' }}></i>
                    <span>{rowData.fileName}</span>
                  </div>
                )}
              />
              <Column 
                field="fileSize"
                header="Size"
                body={(rowData) => formatFileSize(rowData.fileSize)}
                style={{ width: '100px' }}
              />
              <Column 
                field="uploadedDate"
                header="Uploaded" 
                body={(rowData) => formatDate(rowData.uploadedDate)}
                style={{ width: '150px' }}
              />
              <Column 
                field="uploadedBy"
                header="Uploaded By"
                body={(rowData) => rowData.uploadedBy}
                style={{ width: '120px' }}
              />
              <Column
                header="Actions"
                body={downloadActionTemplate}
                style={{ width: '80px', textAlign: 'center' }}
              />
            </DataTable>
          </div>
        </div>
      </Dialog>
    </>
  );
};

export default FilePopup;