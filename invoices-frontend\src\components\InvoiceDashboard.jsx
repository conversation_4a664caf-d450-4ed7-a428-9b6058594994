import React, { useState, useMemo, useEffect } from "react";
import "primereact/resources/themes/lara-light-indigo/theme.css";
import "primereact/resources/primereact.min.css";
import "primeicons/primeicons.css";
import "primeflex/primeflex.css";

import { useInvoices } from "./api/useInvoices";
import { useInvoiceActions } from "./api/useInvoiceActions";
import { useInvoiceContext } from "./InvoiceDashboard/InvoiceHooks";

import FilterBar from './InvoiceDashboard/FilterBar';
import InvoiceToolbar from "./InvoiceDashboard/InvoiceToolbar";
import InvoiceHeaderTable from "./InvoiceDashboard/InvoiceHeaderTable";
import InvoiceDetailTable from "./InvoiceDashboard/InvoiceDetailTable";
import EditPopup from './InvoiceDashboard/EditPopup';
import NotesPopup from "./InvoiceDashboard/NotesPopup";
import LogsPopup from "./InvoiceDashboard/LogsPopup";
import FilePopup from "./InvoiceDashboard/FilePopup";

export default function InvoiceDashboard() {
    // Get state and actions from context
    const {
        filters,
        statusFilter,
        selectedId,
        invoices,
        details,
        allBaOptions,
        loading,
        filtersInitialized,
        isFirstLoad,  // Add this line
        isDataStale,
        updateFilter,
        setStatusFilter,
        setSelectedId,
        setLoading,
        setData,
        resetFiltersAndFetch,
        setFiltersInitialized
    } = useInvoiceContext();
    
    // Local state for popups and editing
    const [notesVisible, setNotesVisible] = useState(false);
    const [logsVisible, setLogsVisible] = useState(false);
    const [editFormVisible, setEditFormVisible] = useState(false);
    const [editLoading, setEditLoading] = useState(false);
    const [pdfAttachmentsVisible, setPdfAttachmentsVisible] = useState(false);
    const [isEditingLocked, setIsEditingLocked] = useState(false);

    // Use the existing useInvoices hook but with modified behavior
    const { fetchData } = useInvoices(filters, !isEditingLocked);
    
    // Initialize filters and load data on component mount
    useEffect(() => {
        // Only run initialization logic if this is truly the first load
        if (!filtersInitialized && isFirstLoad) {
            setFiltersInitialized(true);
            
            // Only fetch if we don't have data or data is stale
            if (invoices.length === 0 || isDataStale) {
                fetchData(filters).then(data => {
                    if (data) {
                        setData(data);
                    }
                });
            }
        }
        // If filters are initialized but we're returning to the page and data is stale, refresh
        else if (filtersInitialized && isDataStale && invoices.length === 0) {
            fetchData(filters).then(data => {
                if (data) {
                    setData(data);
                }
            });
        }
    }, [filtersInitialized, isFirstLoad, invoices.length, isDataStale, fetchData, filters, setFiltersInitialized, setData]);
    
    const onActionComplete = () => {
        if (!isEditingLocked) {
            fetchData(filters).then(data => {
                if (data) {
                    setData(data);
                }
            });
        }
    };
    
    const { buttonLoading, handleProcessClick, handleOverrideClick, handleIgnoreClick } = useInvoiceActions(selectedId, invoices, onActionComplete);

    const filteredHeaders = useMemo(() => {
        const pendingStatuses = ["Staged", "Processing", "Error"];
        const processedStatuses = ["Processed", "Override", "Ignored", "Completed"];
        if (statusFilter === "Pending") return invoices.filter(h => pendingStatuses.includes(h.processingStatus));
        if (statusFilter === "Processed") return invoices.filter(h => processedStatuses.includes(h.processingStatus));
        return [];
    }, [invoices, statusFilter]);

    // Check if selected record is still visible and unselect if not
    useEffect(() => {
        if (selectedId && filteredHeaders.length > 0) {
            const isSelectedVisible = filteredHeaders.some(invoice => invoice.headerID === selectedId);
            if (!isSelectedVisible) {
                setSelectedId(null);
            }
        }
    }, [selectedId, filteredHeaders, setSelectedId]);
    
    const handleFilterApply = () => {
        if (!isEditingLocked) {
            setLoading(true);
            fetchData(filters).then(data => {
                if (data) {
                    setData(data);
                }
            });
        }
    };
    
    const handleFilterReset = () => {
        if (!isEditingLocked) {
            resetFiltersAndFetch((resetFilters) => {
                setLoading(true);
                fetchData(resetFilters).then(data => {
                    if (data) {
                        setData(data);
                    }
                });
            });
        }
    };

    const handleEditClick = () => {
        if (!selectedId) return;
        setEditLoading(true);
        setIsEditingLocked(true);
        setEditFormVisible(true);
        setEditLoading(false);
    };

    const handleEditSave = (result) => {
        if (result && result.success) {
            setEditFormVisible(false);
            setIsEditingLocked(false);
            
            // Use the same pattern as other actions
            setTimeout(() => {
                setLoading(true);
                fetchData(filters).then(data => {
                    if (data) {
                        setData(data);
                    } else {
                        setLoading(false);
                    }
                });
            }, 100);
        } else {
            console.error('Save failed:', result);
        }
    };
    
    const handleEditCancel = () => {
        setEditFormVisible(false);
        setIsEditingLocked(false);
    };

    const clearFilter = (fieldsToClear) => {
        if (!isEditingLocked) {
            const currentFilters = { ...filters };
            fieldsToClear.forEach(f => {
                currentFilters[f] = Array.isArray(currentFilters[f]) ? [] : null;
            });
            // Update multiple fields at once
            Object.keys(currentFilters).forEach(key => {
                if (fieldsToClear.includes(key)) {
                    updateFilter(key, currentFilters[key]);
                }
            });
        }
    };

    // Handle status filter change with auto-unselect
    const handleStatusFilterChange = (newStatus) => {
        setStatusFilter(newStatus);
    };
    
    const handleNotesClick = (headerId) => {
        if (headerId) setSelectedId(headerId);
        setNotesVisible(true);
    };

    const handlePDFClick = () => {
        if (!selectedId) return;
        setPdfAttachmentsVisible(true);
    };
    
    const selectedRowForEdit = selectedId ? invoices.find(d => d.headerID === selectedId) : null;
    const detailsForSelectedRow = selectedId && details ? (Array.isArray(details[selectedId]) ? details[selectedId] : []) : [];

    return (
        <main className="flex-1 p-4 flex flex-column gap-4 bg-gray-50">
          <style>{`
              .matched-detail-row { background-color: #d4edda !important; }
              .highlighted-header-row { background-color: #d4edda !important; }
              .ignored-header-row { background-color: #f8f9fa !important; }
              .editing-locked { opacity: 0.6; pointer-events: none; }
              .stale-data-indicator { 
                  background-color: #fff3cd; 
                  border: 1px solid #ffeaa7; 
                  padding: 8px; 
                  border-radius: 4px; 
                  margin-bottom: 16px;
                  color: #856404;
              }
          `}</style>
          
          <div className={isEditingLocked ? 'editing-locked' : ''}>
              <FilterBar
                  filters={filters}
                  allBaOptions={allBaOptions}
                  onChange={updateFilter}
                  onApply={handleFilterApply}
                  onReset={handleFilterReset}
                  loading={loading}
                  disabled={isEditingLocked}
              />
          </div>

          <div className={isEditingLocked ? 'editing-locked' : ''}>
                <InvoiceToolbar
                    selectedId={selectedId}
                    invoices={invoices}
                    details={details}
                    actions={{ handleProcessClick, handleOverrideClick, handleIgnoreClick }}
                    loadingStates={{ buttonLoading, edit: editLoading }}
                    statusFilter={statusFilter}
                    onStatusFilterChange={handleStatusFilterChange}
                    onEdit={handleEditClick}
                    onNotes={() => selectedId && setNotesVisible(true)}
                    onLogs={() => selectedId && setLogsVisible(true)}
                    onPDF={handlePDFClick}
                    disabled={isEditingLocked}
                />
            </div>

          <div className={isEditingLocked ? 'editing-locked' : ''}>
              <InvoiceHeaderTable
                  invoices={filteredHeaders}
                  loading={loading}
                  selectedId={selectedId}
                  onSelectionChange={setSelectedId}
                  onNotesClick={handleNotesClick}
                  filters={filters}
                  updateFilter={updateFilter}
                  clearFilter={clearFilter}
                  disabled={isEditingLocked}
              />
          </div>

          <div className={isEditingLocked ? 'editing-locked' : ''}>
              <InvoiceDetailTable
                  details={detailsForSelectedRow}
                  disabled={isEditingLocked}
              />
          </div>

          {/* Popups */}
          {editFormVisible && selectedRowForEdit && (
              <EditPopup
                  visible={editFormVisible}
                  data={selectedRowForEdit}
                  details={Array.isArray(detailsForSelectedRow) ? detailsForSelectedRow : []}
                  onHide={handleEditCancel}
                  onSave={handleEditSave}
                  allBaOptions={allBaOptions || []}
              />
          )}
          
          {notesVisible && selectedId && (
              <NotesPopup
                  visible={notesVisible}
                  onHide={() => setNotesVisible(false)}
                  headerID={selectedId}
              />
          )}

          {logsVisible && selectedId && (
              <LogsPopup
                  visible={logsVisible}
                  onHide={() => setLogsVisible(false)}
                  headerID={selectedId}
              />
          )}

          {pdfAttachmentsVisible && selectedId && (
              <FilePopup
                  visible={pdfAttachmentsVisible}
                  onHide={() => setPdfAttachmentsVisible(false)}
                  headerID={selectedId}
              />
          )}
        </main>
    );
}