// hooks/useAuth.js - Updated to use authConfig scopes
import { useMsal, useIsAuthenticated } from '@azure/msal-react';
import { useCallback } from 'react';
import { loginRequest, protectedResources } from '../Auth/authConfig';

export const useAuth = () => {
  const { instance, accounts } = useMsal();
  const isAuthenticated = useIsAuthenticated();

  const getUserInfo = useCallback(() => {
    if (!isAuthenticated || accounts.length === 0) {
      return null;
    }

    const account = accounts[0]; // Use the first account
    return {
      id: account.idTokenClaims?.oid,
      email: account.idTokenClaims?.email || account.username,
      name: account.name,
      tenantId: account.idTokenClaims?.tid,
      rawClaims: account.idTokenClaims
    };
  }, [isAuthenticated, accounts]);

  const getAccessToken = useCallback(async (scopes = null) => {
    if (!isAuthenticated || accounts.length === 0) {
      return null;
    }

    try {
      const account = accounts[0];
      
      // Use provided scopes, or fall back to loginRequest scopes, or default
      const tokenScopes = scopes || loginRequest.scopes || ["User.Read"];
      
      
      const tokenResponse = await instance.acquireTokenSilent({
        account,
        scopes: tokenScopes
      });

      return tokenResponse.accessToken;
    } catch (error) {
      
      // Handle interaction required error
      if (error.name === 'InteractionRequiredAuthError') {
        try {
          const tokenScopes = scopes || loginRequest.scopes || ["User.Read"];
          const popupResponse = await instance.acquireTokenPopup({
            account: accounts[0],
            scopes: tokenScopes
          });
          return popupResponse.accessToken;
        } catch (popupError) {
          return popupError;
        }
      }
      
      return null;
    }
  }, [isAuthenticated, accounts, instance]);

  const getAuthHeaders = useCallback(async (scopes = null) => {
    const headers = {};
    
    const userInfo = getUserInfo();
    const token = await getAccessToken(scopes);
    
    if (userInfo) {
      headers['X-User-Id'] = userInfo.id;
      headers['X-User-Email'] = userInfo.email;
      headers['X-User-Name'] = userInfo.name;
    }

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return headers;
  }, [getUserInfo, getAccessToken]);

  return {
    isAuthenticated,
    accounts,
    instance,
    getUserInfo,
    getAccessToken,
    getAuthHeaders,
    // Expose the scopes for easy access
    defaultScopes: loginRequest.scopes,
    graphScopes: protectedResources.graphMe.scopes
  };
};