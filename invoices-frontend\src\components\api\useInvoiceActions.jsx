import { useState } from 'react';
import { useApi } from './useApi';
import apiService from './apiService';
import { useAuth } from '../../Auth/useAuth';

export const useInvoiceActions = (selectedId, invoices, onActionComplete) => {
    const { callApi, loading } = useApi();
    const { getAuthHeaders } = useAuth();
    const [buttonLoading, setButtonLoading] = useState({ process: false, override: false, ignore: false });

    const performAction = async (actionType, apiCall, successMessage) => {
        if (!selectedId) return;
        setButtonLoading(prev => ({ ...prev, [actionType]: true }));
        try {
            await callApi(apiCall);
            if (onActionComplete) {
                onActionComplete(successMessage); // Optionally pass message to callback
            }
        } catch (error) {
            // Handle error appropriately
            console.error('Action failed:', error);
        } finally {
            setButtonLoading(prev => ({ ...prev, [actionType]: false }));
        }
    };

    const handleProcessClick = () => {
        const selectedRow = invoices.find(d => d.headerID === selectedId);
        if (!selectedRow) return;

        let endpoint, params = {}, successMessage;
        if (selectedRow.processingStatus === "Processing") {
            endpoint = `/Invoices/Cancel/`;
            params.headerID = selectedId;
            successMessage = "Processing cancelled successfully";
        } else if (["Processed", "Override"].includes(selectedRow.processingStatus)) {
            endpoint = `/Invoices/Reprocess/`;
            params.headerID = selectedId;
            successMessage = "Invoice reprocessed successfully";
        } else {
            endpoint = `/Invoices/Process/`;
            params.HeaderID = selectedId;
            successMessage = "Invoice processing started";
        }

        performAction('process', () => apiService.post(endpoint, params, {}, getAuthHeaders), successMessage);
    };

    const handleOverrideClick = () => {
        const params = { headerID: selectedId };
        performAction(
            'override',
            () => apiService.post(`/Invoices/Override/`, params, {}, getAuthHeaders),
            "Invoice overridden successfully"
        );
    };

    const handleIgnoreClick = () => {
        const selectedRow = invoices.find(d => d.headerID === selectedId);
        if (!selectedRow) return;

        const params = { headerID: selectedId };
        const isIgnored = selectedRow.processingStatus === "Ignored";
        const endpoint = isIgnored ? `/Invoices/UnIgnore/` : `/Invoices/Ignore/`;
        const successMessage = isIgnored ? "Invoice unignored successfully" : "Invoice ignored successfully";

        performAction('ignore', () => apiService.post(endpoint, params, {}, getAuthHeaders), successMessage);
    };

    return {
        buttonLoading,
        handleProcessClick,
        handleOverrideClick,
        handleIgnoreClick,
        loading
    };
};