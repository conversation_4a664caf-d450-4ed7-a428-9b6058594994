import React from 'react';
import { Card } from 'primereact/card';
import { Panel } from 'primereact/panel';
import { Badge } from 'primereact/badge';
import { Message } from 'primereact/message';
import { Divider } from 'primereact/divider';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';

const InvoiceTablesHelp = () => {
  const headerFields = [
    {
      field: 'Status Indicator',
      description: 'Visual icon showing the current processing status of the invoice.',
      dataType: 'Icon',
      values: [
        { status: 'Processing', icon: 'pi-arrow-right', color: '#17a2b8', description: 'Invoice is currently being processed' },
        { status: 'Error', icon: 'pi-times', color: '#dc3545', description: 'Processing encountered an error' },
        { status: 'Processed', icon: 'pi-check', color: '#28a745', description: 'Successfully processed' },
        { status: 'Pending', icon: 'pi-clock', color: '#ffc107', description: 'Waiting for processing' },
        { status: 'Ignored', icon: 'pi-ban', color: '#6c757d', description: 'Marked as ignored/cancelled' },
        { status: 'Override', icon: 'pi-exclamation-triangle', color: '#fd7e14', description: 'Manual override applied' },
        { status: 'Staged', icon: 'pi-circle', color: '#6c757d', description: 'Staged for processing' }
      ]
    },
    {
      field: 'hasNotes',
      description: 'Indicates if the invoice has associated notes or comments.',
      dataType: 'Boolean/Icon',
      values: 'Shows file icon when notes exist, empty when no notes'
    },
    {
      field: 'externalCounterparty',
      description: 'The external business partner or vendor name as it appears on the invoice.',
      dataType: 'Text',
      values: 'Company names, vendor identifiers'
    },
    {
      field: 'internalCounterparty',
      description: 'The internal system representation of the business partner after mapping.',
      dataType: 'Text',
      values: 'Standardized internal company names'
    },
    {
      field: 'template',
      description: 'The processing template or format used for this invoice type.',
      dataType: 'Text',
      values: 'Template names, processing rules identifiers'
    },
    {
      field: 'invoiceNumber',
      description: 'The unique invoice identifier from the external source.',
      dataType: 'Text/Number',
      values: 'Invoice numbers, reference codes'
    },
    {
      field: 'invoiceDate',
      description: 'The date the invoice was issued by the vendor.',
      dataType: 'Date',
      values: 'MM/DD/YYYY format'
    },
    {
      field: 'dueDate',
      description: 'The payment due date for the invoice.',
      dataType: 'Date',
      values: 'MM/DD/YYYY format'
    },
    {
      field: 'paymentTerms',
      description: 'The agreed payment terms for the invoice.',
      dataType: 'Text',
      values: 'Net 30, COD, etc.'
    },
    {
      field: 'totalAmount',
      description: 'The total invoice amount including all line items.',
      dataType: 'Currency',
      values: 'Dollar amounts with 2 decimal places'
    },
    {
      field: 'status',
      description: 'The current business status of the invoice.',
      dataType: 'Text',
      values: 'Active, Pending, Completed, etc.'
    }
  ];

  const detailFields = [
    {
      field: 'bol',
      description: 'Bill of Lading number for the shipment transaction.',
      dataType: 'Text',
      values: 'BOL numbers, shipping references'
    },
    {
      field: 'transactionType',
      description: 'The type of transaction or movement being invoiced.',
      dataType: 'Text',
      values: 'Sale, Purchase, Transport, Storage'
    },
    {
      field: 'gravity',
      description: 'API gravity measurement for petroleum products.',
      dataType: 'Number',
      values: 'Decimal values (e.g., 35.2)'
    },
    {
      field: 'origin',
      description: 'The source location where the product originated.',
      dataType: 'Text',
      values: 'Terminal names, facility locations'
    },
    {
      field: 'destination',
      description: 'The delivery location for the product.',
      dataType: 'Text',
      values: 'Terminal names, delivery points'
    },
    {
      field: 'product',
      description: 'The specific product or commodity being invoiced.',
      dataType: 'Text',
      values: 'Crude Oil, Gasoline, Diesel, etc.'
    },
    {
      field: 'uoM',
      description: 'Unit of Measure for the quantity being invoiced.',
      dataType: 'Text',
      values: 'Barrels, Gallons, Metric Tons'
    },
    {
      field: 'quantity',
      description: 'The amount of product in the specified unit of measure.',
      dataType: 'Number',
      values: 'Decimal quantities'
    },
    {
      field: 'rate',
      description: 'The price per unit for the product or service.',
      dataType: 'Currency',
      values: 'Dollar amounts per unit'
    },
    {
      field: 'amount',
      description: 'The total line item amount (quantity × rate).',
      dataType: 'Currency',
      values: 'Dollar amounts with 2 decimal places'
    },
    {
      field: 'transactionDate',
      description: 'The date when the transaction or movement occurred.',
      dataType: 'Date',
      values: 'MM/DD/YYYY format'
    },
    {
      field: 'isMatched',
      description: 'Indicates if the detail line has been matched to internal records.',
      dataType: 'Boolean',
      values: 'True (highlighted green), False (normal display)'
    }
  ];

  const tableFeatures = [
    {
      feature: 'Row Selection',
      description: 'Click any header row to select it and view its details.',
      behavior: 'Selected row highlighted, detail table updates automatically'
    },
    {
      feature: 'Status Color Coding',
      description: 'Rows are color-coded based on processing status.',
      behavior: 'Green for processed/override, gray for ignored, white for pending'
    },
    {
      feature: 'Sorting',
      description: 'Click column headers to sort data ascending/descending.',
      behavior: 'Arrow indicators show current sort direction'
    },
    {
      feature: 'Pagination',
      description: 'Navigate through large datasets using page controls.',
      behavior: 'Configurable rows per page, jump to specific page'
    },
    {
      feature: 'Detail Matching',
      description: 'Detail rows highlighted when matched to internal records.',
      behavior: 'Green background indicates successful data matching'
    },
    {
      feature: 'Notes Indicator',
      description: 'File icon shows when notes are available for an invoice.',
      behavior: 'Click icon to open notes popup'
    }
  ];

  const headerTemplate = (
    <div className="bg-primary text-primary-50 p-4 border-round">
      <h1 className="text-3xl font-bold m-0 mb-2">Invoice Tables Reference - User Manual</h1>
      <p className="text-lg m-0 text-primary-100">
        Complete guide to understanding invoice header and detail table data
      </p>
    </div>
  );

  const statusIconTemplate = (rowData) => (
    <div className="flex align-items-center gap-2">
      <i className={`pi ${rowData.icon}`} style={{ color: rowData.color, fontSize: '14px' }} />
      <span className="text-sm">{rowData.status}</span>
      <span className="text-xs text-600">- {rowData.description}</span>
    </div>
  );

  return (
    <div className="flex flex-column gap-4">
      {/* Header Section */}
      <Card className="shadow-2">
        {headerTemplate}
      </Card>

      {/* Table Overview */}
      <Card title="Table Structure Overview" className="shadow-2">
        <Message 
          severity="info" 
          text="The Invoice Dashboard displays data in a master-detail relationship with two main tables: Invoice Headers (master records) and Invoice Details (related line items). Selection in the header table automatically updates the detail table."
          className="mb-4"
        />
        
        <div className="grid">
          <div className="col-12 md:col-6">
            <Panel header="Invoice Header Table" className="h-full">
              <p className="text-600 text-sm mb-3">
                Master table showing one row per invoice with summary information and processing status.
              </p>
              <ul className="text-600 text-sm pl-3 m-0">
                <li>One record per invoice document</li>
                <li>Selectable rows to view details</li>
                <li>Color-coded by processing status</li>
                <li>Sortable and paginated</li>
                <li>Contains counterparty and amount data</li>
              </ul>
            </Panel>
          </div>

          <div className="col-12 md:col-6">
            <Panel header="Invoice Detail Table" className="h-full">
              <p className="text-600 text-sm mb-3">
                Detail table showing line items for the selected invoice with transaction-specific data.
              </p>
              <ul className="text-600 text-sm pl-3 m-0">
                <li>Multiple records per selected invoice</li>
                <li>Transaction and product details</li>
                <li>Quantity and pricing information</li>
                <li>Matching status indicators</li>
                <li>Location and movement data</li>
              </ul>
            </Panel>
          </div>
        </div>
      </Card>

      {/* Header Table Fields */}
      <Card title="Invoice Header Table Fields" className="shadow-2">
        <p className="text-600 mb-4">
          The header table contains summary information for each invoice, including processing status and key business data:
        </p>
        
        <DataTable 
          value={headerFields} 
          className="mb-4"
          showGridlines
          stripedRows
          scrollable
          scrollHeight="400px"
        >
          <Column 
            field="field" 
            header="Field Name" 
            style={{ width: '20%' }}
            body={(rowData) => <Badge value={rowData.field} severity="primary" className="text-xs" />}
          />
          <Column 
            field="description" 
            header="Description" 
            style={{ width: '40%' }}
          />
          <Column 
            field="dataType" 
            header="Data Type" 
            style={{ width: '15%' }}
            body={(rowData) => <span className="text-sm font-mono bg-gray-100 px-2 py-1 border-round">{rowData.dataType}</span>}
          />
          <Column 
            field="values" 
            header="Sample Values / Behavior" 
            style={{ width: '25%' }}
            body={(rowData) => (
              <div className="text-sm text-600">
                {Array.isArray(rowData.values) ? (
                  <div className="flex flex-column gap-1">
                    {rowData.values.map((value, index) => (
                      <div key={index} className="flex align-items-center gap-2">
                        <i className={`pi ${value.icon}`} style={{ color: value.color, fontSize: '12px' }} />
                        <span>{value.status}</span>
                      </div>
                    ))}
                  </div>
                ) : rowData.values}
              </div>
            )}
          />
        </DataTable>

        <h4 className="text-primary mb-3">Status Indicator Reference</h4>
        <div className="surface-50 border-round p-3">
          <div className="grid">
            {headerFields[0].values.map((status, index) => (
              <div key={index} className="col-12 md:col-6 lg:col-4 mb-2">
                {statusIconTemplate(status)}
              </div>
            ))}
          </div>
        </div>
      </Card>

      {/* Detail Table Fields */}
      <Card title="Invoice Detail Table Fields" className="shadow-2">
        <p className="text-600 mb-4">
          The detail table shows line-item information for the selected invoice, including transaction details and matching status:
        </p>
        
        <DataTable 
          value={detailFields} 
          className="mb-4"
          showGridlines
          stripedRows
          scrollable
          scrollHeight="400px"
        >
          <Column 
            field="field" 
            header="Field Name" 
            style={{ width: '20%' }}
            body={(rowData) => <Badge value={rowData.field} severity="success" className="text-xs" />}
          />
          <Column 
            field="description" 
            header="Description" 
            style={{ width: '45%' }}
          />
          <Column 
            field="dataType" 
            header="Data Type" 
            style={{ width: '15%' }}
            body={(rowData) => <span className="text-sm font-mono bg-gray-100 px-2 py-1 border-round">{rowData.dataType}</span>}
          />
          <Column 
            field="values" 
            header="Sample Values" 
            style={{ width: '20%' }}
            body={(rowData) => <span className="text-sm text-600">{rowData.values}</span>}
          />
        </DataTable>

        <div className="surface-100 border-round p-3">
          <h5 className="text-primary mb-2">Detail Row Color Coding</h5>
          <div className="grid">
            <div className="col-12 md:col-6">
              <div className="flex align-items-center gap-2 mb-2">
                <div className="w-4 h-2 bg-green-200 border-round"></div>
                <span className="text-sm"><strong>Matched Records:</strong> Detail has been successfully matched to internal system records</span>
              </div>
            </div>
            <div className="col-12 md:col-6">
              <div className="flex align-items-center gap-2 mb-2">
                <div className="w-4 h-2 bg-white border-1 border-gray-300 border-round"></div>
                <span className="text-sm"><strong>Unmatched Records:</strong> Detail requires matching or manual processing</span>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Table Features */}
      <Card title="Table Features & Interaction" className="shadow-2">
        <p className="text-600 mb-4">
          Both tables provide interactive features to help navigate and understand the invoice data:
        </p>
        
        <div className="grid">
          {tableFeatures.map((feature, index) => (
            <div key={index} className="col-12 md:col-6">
              <Panel 
                header={
                  <div className="flex align-items-center">
                    <span className="font-semibold">{feature.feature}</span>
                  </div>
                } 
                className="mb-3 h-full"
                toggleable
              >
                <p className="text-700 mb-3">{feature.description}</p>
                <div className="surface-100 border-round p-3">
                  <h6 className="text-primary mb-2">Behavior</h6>
                  <p className="text-600 text-sm m-0">{feature.behavior}</p>
                </div>
              </Panel>
            </div>
          ))}
        </div>
      </Card>

      {/* Data Relationships */}
      <Card title="Data Relationships & Flow" className="shadow-2">
        <div className="grid">
          <div className="col-12 md:col-6">
            <h4 className="text-primary mb-3">Master-Detail Relationship</h4>
            <ol className="text-600 text-sm pl-3 m-0">
              <li>Invoice headers represent complete invoice documents</li>
              <li>Each header can have multiple detail line items</li>
              <li>Selecting a header loads its associated details</li>
              <li>Detail table updates automatically with selection changes</li>
              <li>Unselected state shows empty detail table</li>
            </ol>
          </div>
          <div className="col-12 md:col-6">
            <h4 className="text-primary mb-3">Processing Status Flow</h4>
            <div className="flex flex-column gap-2">
              <div className="flex align-items-center gap-2">
                <i className="pi pi-circle text-gray-500" />
                <span className="text-sm">Staged → Ready for processing</span>
              </div>
              <div className="flex align-items-center gap-2">
                <i className="pi pi-arrow-right text-blue-500" />
                <span className="text-sm">Processing → Currently being processed</span>
              </div>
              <div className="flex align-items-center gap-2">
                <i className="pi pi-check text-green-500" />
                <span className="text-sm">Processed → Successfully completed</span>
              </div>
              <div className="flex align-items-center gap-2">
                <i className="pi pi-times text-red-500" />
                <span className="text-sm">Error → Requires attention</span>
              </div>
              <div className="flex align-items-center gap-2">
                <i className="pi pi-ban text-gray-500" />
                <span className="text-sm">Ignored → Excluded from processing</span>
              </div>
            </div>
          </div>
        </div>
        
        <Divider />
        
        <Message 
          severity="success" 
          text="Use the combination of header selection and detail matching status to understand the complete processing state of each invoice and identify any items requiring manual attention."
        />
      </Card>

      {/* Best Practices */}
      <Card title="Data Interpretation Best Practices" className="shadow-2">
        <div className="grid">
          <div className="col-12 md:col-6">
            <h4 className="text-green-600 mb-3">Reading the Data</h4>
            <ul className="text-600 text-sm pl-3 m-0">
              <li>Check status indicators first for processing state</li>
              <li>Review total amounts for accuracy</li>
              <li>Verify counterparty mapping is correct</li>
              <li>Look for notes indicators for additional context</li>
              <li>Use detail matching to validate line items</li>
              <li>Compare invoice and transaction dates for timing</li>
              <li>Verify origin/destination locations make sense</li>
            </ul>
          </div>
          <div className="col-12 md:col-6">
            <h4 className="text-orange-600 mb-3">Common Issues to Watch</h4>
            <ul className="text-600 text-sm pl-3 m-0">
              <li>Mismatched external vs internal counterparties</li>
              <li>Unmatched detail rows requiring attention</li>
              <li>Error status invoices needing investigation</li>
              <li>Missing or invalid date information</li>
              <li>Quantity and amount calculation discrepancies</li>
              <li>Incomplete location or product mapping</li>
              <li>Template assignment issues</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default InvoiceTablesHelp;