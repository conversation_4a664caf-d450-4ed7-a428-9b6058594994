import React from 'react';
import { Card } from 'primereact/card';
import { Panel } from 'primereact/panel';
import { Badge } from 'primereact/badge';
import { Message } from 'primereact/message';
import { Divider } from 'primereact/divider';
import { Button } from 'primereact/button';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';

const XRefHelp = () => {
  const categories = [
    {
      category: 'BusinessAssociate',
      inputType: 'Text Input',
      outputType: 'BusinessAssociate Lookup',
      description: 'Maps incoming ocr text for internal/external counterparty to RightAngle BA tables.',
      usage: 'Used to make sure all incoming text matches correctly to a RightAngle record.',
      example: 'Input: "ABC Corp" → Output: "ABC Corporation (ID: 123)"'
    },
    {
      category: 'Currency',
      inputType: 'Text Input',
      outputType: 'Currency Lookup',
      description: 'Maps incoming ocr currency text to RightAngle currency tables.',
      usage: 'Used to make sure all incoming text matches correctly to a RightAngle record.',
      example: 'Input: "USD" → Output: "US Dollar (ID: 1)"'
    },
    {
      category: 'Product',
      inputType: 'Text Input',
      outputType: 'Product Lookup',
      description: 'Maps incoming ocr product text to RightAngle product tables.',
      usage: 'Used to make sure all incoming text matches correctly to a RightAngle record.',
      example: 'Input: "Crude Oil" → Output: "WTI Crude Oil (ID: 456)"'
    },
    {
      category: 'Users',
      inputType: 'Text Input',
      outputType: 'Users Lookup',
      description: 'Maps users windows account to RightAngle database account in case of any differences.',
      usage: 'Used in cases where the internal RightAngle ID does not match with a users windows account.',
      example: 'Input: "<EMAIL>" → Output: "John Doe (ID: 789)"'
    },
    {
      category: 'Locale',
      inputType: 'Text Input',
      outputType: 'Locale Lookup',
      description: 'Maps location names or codes to standardized locale records.',
      usage: 'Used to make sure all incoming text matches correctly to a RightAngle record.',
      example: 'Input: "Houston TX" → Output: "Houston Terminal (ID: 321)"'
    },
    {
      category: 'Office',
      inputType: 'BusinessAssociate Lookup',
      outputType: 'Locale Lookup',
      description: 'Maps BAs to their SAP billing location.',
      usage: 'Used for mapping the remit to field, which maps to the SAP location. Field is needed for loading invoices to SAP',
      example: 'Input: "ABC Corp Houston Office" → Output: "Houston Terminal (ID: 321)"'
    }
  ];

  const fieldTypes = [
    {
      type: 'Text Input',
      description: 'Free-form text entry for mapping external values.',
      behavior: 'User can type any string value that needs to be mapped.',
      validation: 'No specific format requirements - accepts any text input.'
    },
    {
      type: 'Dropdown (Lookup)',
      description: 'Selection from predefined system records.',
      behavior: 'Dropdown populated from corresponding database table.',
      validation: 'Must select from available options - cannot enter custom text.'
    }
  ];

  const actions = [
    {
      action: 'Add XRef',
      icon: 'pi pi-plus',
      description: 'Create a new cross-reference mapping.',
      usage: 'Click to open dialog and create new input/output mapping.',
      behavior: [
        'Opens dialog with empty fields',
        'Pre-selects category if one is filtered',
        'Loads appropriate field types based on category',
        'Validates required fields before saving'
      ],
      requirements: ['User must have write permissions']
    },
    {
      action: 'Edit XRef',
      icon: 'pi pi-pencil',
      description: 'Modify existing cross-reference mapping.',
      usage: 'Click pencil icon on any row to edit that mapping.',
      behavior: [
        'Opens dialog pre-populated with existing values',
        'Loads dropdown options for selected category',
        'Preserves original IDs for lookup fields',
        'Updates record when saved'
      ],
      requirements: ['User must have write permissions']
    },
    {
      action: 'Delete XRef',
      icon: 'pi pi-trash',
      description: 'Remove cross-reference mapping permanently.',
      usage: 'Click trash icon and confirm deletion.',
      behavior: [
        'Shows confirmation dialog with mapping details',
        'Permanently removes record from system',
        'Cannot be undone once confirmed',
        'Refreshes table after successful deletion'
      ],
      requirements: ['User must have delete permissions', 'Confirmation required']
    },
    {
      action: 'Filter by Category',
      icon: 'pi pi-filter',
      description: 'Show only mappings for selected category.',
      usage: 'Select category from dropdown to filter results.',
      behavior: [
        'Filters table to show only selected category',
        'Shows all categories when "All Categories" selected',
        'Maintains search filter while category filtering',
        'Pre-selects category in Add dialog when filtered'
      ],
      requirements: ['No special requirements']
    },
    {
      action: 'Search XRefs',
      icon: 'pi pi-search',
      description: 'Search across all mapping fields.',
      usage: 'Type in search box to filter mappings by any field.',
      behavior: [
        'Searches input, output, and category fields',
        'Updates results in real-time as you type',
        'Combines with category filter if active',
        'Case-insensitive partial matching'
      ],
      requirements: ['No special requirements']
    },
    {
      action: 'Refresh Data',
      icon: 'pi pi-refresh',
      description: 'Reload all cross-reference data from server.',
      usage: 'Click refresh button to get latest data.',
      behavior: [
        'Fetches current data from database',
        'Shows loading indicator during refresh',
        'Maintains current filters after refresh',
        'Useful after other users make changes'
      ],
      requirements: ['No special requirements']
    }
  ];

  const workflowSteps = [
    {
      step: 1,
      title: 'Identify Mapping Need',
      description: 'Determine which external values need to be mapped to internal records.',
      details: 'Review data import errors or inconsistencies to identify unmapped values.'
    },
    {
      step: 2,
      title: 'Select Category',
      description: 'Choose the appropriate category for the mapping type.',
      details: 'Category determines which lookup tables are available for input/output fields.'
    },
    {
      step: 3,
      title: 'Create Mapping',
      description: 'Add new cross-reference with input and output values.',
      details: 'Input is the external value, output is the corresponding internal record.'
    },
    {
      step: 4,
      title: 'Test Mapping',
      description: 'Verify the mapping works correctly in data processing.',
      details: 'Process sample data to ensure mappings are applied correctly.'
    },
    {
      step: 5,
      title: 'Maintain Mappings',
      description: 'Regularly review and update mappings as needed.',
      details: 'Add new mappings for new external sources and update existing ones.'
    }
  ];

  const headerTemplate = (
    <div className="bg-primary text-primary-50 p-4 border-round">
      <h1 className="text-3xl font-bold m-0 mb-2">Cross Reference Management - User Manual</h1>
      <p className="text-lg m-0 text-primary-100">
        Complete guide to managing data mapping and standardization
      </p>
    </div>
  );

  const categoryRowTemplate = (rowData) => (
    <div className="flex flex-column gap-1">
      <div className="font-medium">{rowData.example}</div>
      <div className="text-sm text-600">{rowData.usage}</div>
    </div>
  );

  return (
    <div className="flex flex-column gap-4">
      {/* Header Section */}
      <Card className="shadow-2">
        {headerTemplate}
      </Card>

      {/* Screen Overview */}
      <Card title="System Overview" className="shadow-2">
        <Message 
          severity="info" 
          text="The Cross Reference (XRef) system maps external data values to internal system records, enabling consistent data processing across different sources. This standardization is critical for accurate invoice processing and data integrity."
          className="mb-4"
        />
        
        <div className="grid">
          <div className="col-12 md:col-4">
            <Panel header="Data Standardization" className="h-full">
              <p className="text-600 text-sm mb-3">
                Converts inconsistent external data into standardized internal records for processing.
              </p>
              <ul className="text-600 text-sm pl-3 m-0">
                <li>Vendor name variations</li>
                <li>Product code differences</li>
                <li>Location naming inconsistencies</li>
                <li>Currency format variations</li>
              </ul>
            </Panel>
          </div>

          <div className="col-12 md:col-4">
            <Panel header="Category Management" className="h-full">
              <p className="text-600 text-sm mb-3">
                Organized by data type categories with specific input/output field configurations.
              </p>
              <ul className="text-600 text-sm pl-3 m-0">
                <li>BusinessAssociate mappings</li>
                <li>Product standardization</li>
                <li>Location normalization</li>
                <li>User account linking</li>
              </ul>
            </Panel>
          </div>

          <div className="col-12 md:col-4">
            <Panel header="Processing Integration" className="h-full">
              <p className="text-600 text-sm mb-3">
                Automatically applies mappings during data import and invoice processing workflows.
              </p>
              <ul className="text-600 text-sm pl-3 m-0">
                <li>Real-time mapping application</li>
                <li>Error reduction in processing</li>
                <li>Improved data quality</li>
                <li>Automated standardization</li>
              </ul>
            </Panel>
          </div>
        </div>
      </Card>

      {/* Categories Reference */}
      <Card title="Category Reference" className="shadow-2">
        <p className="text-600 mb-4">
          Each category defines how external values are mapped to internal records. Input and output field types vary by category:
        </p>
        
        <DataTable 
          value={categories} 
          className="mb-4"
          showGridlines
          stripedRows
        >
          <Column 
            field="category" 
            header="Category" 
            style={{ width: '15%' }}
            body={(rowData) => <Badge value={rowData.category} severity="info" />}
          />
          <Column 
            field="inputType" 
            header="Input Type" 
            style={{ width: '15%' }}
          />
          <Column 
            field="outputType" 
            header="Output Type" 
            style={{ width: '20%' }}
          />
          <Column 
            field="description" 
            header="Description" 
            style={{ width: '25%' }}
          />
          <Column 
            field="usage" 
            header="Usage & Example" 
            style={{ width: '25%' }}
            body={categoryRowTemplate}
          />
        </DataTable>

        <h4 className="text-primary mb-3">Field Type Behavior</h4>
        <div className="grid">
          {fieldTypes.map((type, index) => (
            <div key={index} className="col-12 md:col-6">
              <div className="surface-50 border-round p-3 border-left-3 border-blue-500">
                <h5 className="m-0 mb-2 text-primary">{type.type}</h5>
                <p className="text-600 text-sm mb-2">{type.description}</p>
                <div className="mb-2">
                  <strong className="text-sm">Behavior:</strong>
                  <p className="text-600 text-sm m-0 mt-1">{type.behavior}</p>
                </div>
                <div>
                  <strong className="text-sm">Validation:</strong>
                  <p className="text-600 text-sm m-0 mt-1">{type.validation}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Available Actions */}
      <Card title="Available Actions" className="shadow-2">
        <p className="text-600 mb-4">
          The XRef management screen provides comprehensive tools for creating, editing, and managing cross-reference mappings:
        </p>
        
        <div className="grid">
          {actions.map((action, index) => (
            <div key={index} className="col-12">
              <Panel 
                header={
                  <div className="flex align-items-center">
                    <i className={`${action.icon} text-primary mr-2`}></i>
                    <span className="font-semibold">{action.action}</span>
                  </div>
                } 
                className="mb-3"
                toggleable
              >
                <div className="grid">
                  <div className="col-12 md:col-8">
                    <p className="text-700 mb-3">{action.description}</p>
                    
                    <div className="mb-3">
                      <h5 className="text-primary mb-2">Usage:</h5>
                      <p className="text-600 text-sm m-0">{action.usage}</p>
                    </div>

                    <div className="mb-3">
                      <h5 className="text-primary mb-2">Requirements:</h5>
                      <ul className="text-600 text-sm pl-3 m-0">
                        {action.requirements.map((req, reqIndex) => (
                          <li key={reqIndex}>{req}</li>
                        ))}
                      </ul>
                    </div>

                    <div className="surface-100 border-round p-3">
                      <h5 className="text-primary mb-2">Behavior</h5>
                      <ul className="text-600 text-sm pl-3 m-0">
                        {action.behavior.map((behavior, behaviorIndex) => (
                          <li key={behaviorIndex}>{behavior}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                  
                  <div className="col-12 md:col-4">
                    <div className="surface-50 border-round p-3 text-center">
                      <h6 className="text-primary mb-3">Button Examples</h6>
                      <div className="flex flex-column gap-3 align-items-center">
                        {action.action === 'Add XRef' && (
                          <>
                            <Button 
                              icon="pi pi-plus" 
                              label="Add XRef"
                              className="p-button-sm"
                            />
                            <span className="text-sm text-600">Add New Mapping</span>
                          </>
                        )}
                        
                        {action.action === 'Edit XRef' && (
                          <>
                            <Button 
                              icon="pi pi-pencil" 
                              className="p-button-sm" 
                              rounded
                              outlined
                              tooltip="Edit mapping"
                            />
                            <span className="text-sm text-600">Edit Button</span>
                          </>
                        )}
                        
                        {action.action === 'Delete XRef' && (
                          <>
                            <Button 
                              icon="pi pi-trash" 
                              className="p-button-danger p-button-sm" 
                              rounded
                              outlined
                              tooltip="Delete mapping"
                            />
                            <span className="text-sm text-600">Delete Button</span>
                          </>
                        )}
                        
                        {action.action === 'Refresh Data' && (
                          <>
                            <Button 
                              icon="pi pi-refresh" 
                              label="Refresh"
                              className="p-button-sm"
                              outlined
                            />
                            <span className="text-sm text-600">Refresh Button</span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </Panel>
            </div>
          ))}
        </div>
      </Card>

      {/* Workflow Guide */}
      <Card title="Recommended Workflow" className="shadow-2">
        <p className="text-600 mb-4">
          Follow this systematic approach to effectively manage cross-reference mappings:
        </p>
        
        <div className="grid">
          {workflowSteps.map((step, index) => (
            <div key={index} className="col-12">
              <div className="flex align-items-start mb-3">
                <div className="bg-primary text-primary-50 border-round flex align-items-center justify-content-center" 
                     style={{ minWidth: '2rem', minHeight: '2rem', marginRight: '1rem' }}>
                  <span className="font-bold">{step.step}</span>
                </div>
                <div className="flex-1">
                  <h5 className="text-primary m-0 mb-2">{step.title}</h5>
                  <p className="text-700 text-sm mb-2">{step.description}</p>
                  <p className="text-600 text-sm m-0">{step.details}</p>
                </div>
              </div>
              {index < workflowSteps.length - 1 && <Divider />}
            </div>
          ))}
        </div>
      </Card>

      {/* Best Practices */}
      <Card title="Best Practices" className="shadow-2">
        <div className="grid">
          <div className="col-12 md:col-6">
            <h4 className="text-green-600 mb-3">Recommended Practices</h4>
            <ul className="text-600 text-sm pl-3 m-0">
              <li>Use consistent naming conventions for input values</li>
              <li>Test mappings with sample data before deployment</li>
              <li>Document the source of external values being mapped</li>
              <li>Regularly review and clean up unused mappings</li>
              <li>Create mappings for common variations upfront</li>
              <li>Use category filtering when managing large datasets</li>
              <li>Backup mappings before making bulk changes</li>
            </ul>
          </div>
          <div className="col-12 md:col-6">
            <h4 className="text-orange-600 mb-3">Common Issues</h4>
            <ul className="text-600 text-sm pl-3 m-0">
              <li>Case sensitivity differences in input values</li>
              <li>Extra whitespace or special characters</li>
              <li>Duplicate mappings with different outputs</li>
              <li>Outdated mappings pointing to inactive records</li>
              <li>Missing mappings for common external values</li>
              <li>Incorrect category selection for mapping type</li>
              <li>Circular or contradictory mapping relationships</li>
            </ul>
          </div>
        </div>
        
        <Divider />
        
        <Message 
          severity="success" 
          text="Maintain a regular schedule for reviewing and updating cross-reference mappings to ensure data quality and processing accuracy. New external data sources often require new mappings."
        />
      </Card>

      {/* Troubleshooting */}
      <Card title="Troubleshooting Guide" className="shadow-2">
        <div className="grid">
          <div className="col-12 md:col-6">
            <h4 className="text-primary mb-3">Data Not Mapping</h4>
            <ol className="text-600 text-sm pl-3 m-0">
              <li>Verify input value matches exactly (check for spaces/case)</li>
              <li>Confirm mapping exists in correct category</li>
              <li>Check that output record is still active</li>
              <li>Refresh mappings if recently added</li>
              <li>Review system logs for mapping errors</li>
            </ol>
          </div>
          <div className="col-12 md:col-6">
            <h4 className="text-primary mb-3">Dialog Not Loading Options</h4>
            <ol className="text-600 text-sm pl-3 m-0">
              <li>Ensure category is selected first</li>
              <li>Check network connectivity for API calls</li>
              <li>Verify user has read access to lookup tables</li>
              <li>Refresh page if dropdown options are stale</li>
              <li>Contact administrator for permission issues</li>
            </ol>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default XRefHelp;