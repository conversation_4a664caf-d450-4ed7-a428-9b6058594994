// SideMenuBar.jsx
import React, { useEffect } from 'react';
import { MegaMenu } from 'primereact/megamenu';
import { useNavigate, useLocation } from 'react-router-dom';

const SideMenuBar = ({ isCollapsed, setIsCollapsed }) => {
  let navigate = useNavigate();
  let location = useLocation();

  useEffect(() => {
    document.activeElement.blur();
  }, [location.pathname, isCollapsed]);

  const items = [
    {
      label: 'Dashboard',
      icon: 'pi pi-home',
      command: () => {
        navigate('/');
        document.activeElement.blur();
      },
      path: '/'
    },
    {
      label: 'Admin',
      icon: 'pi pi-cog',
      command: () => {
        navigate('/Admin');
        document.activeElement.blur();
      },
      path: '/Admin'
    },
    {
      label: 'Logs',
      icon: 'pi pi-history',
      command: () => {
        navigate('/Logs');
        document.activeElement.blur();
      },
      path: '/Logs'
    },
    {
      label: 'Status',
      icon: 'pi pi-bolt',
      command: () => {
        navigate('/Status');
        document.activeElement.blur();
      },
      path: '/Status'
    },
    {
      label: 'XRef',
      icon: 'pi pi-link',
      command: () => {
        navigate('/XRef');
        document.activeElement.blur();
      },
      path: '/XRef'
    },
    {
      label: 'Help',
      icon: 'pi pi-question-circle',
      command: () => {
        navigate('/Help');
        document.activeElement.blur();
      },
      path: '/Help'
    },
    {
      label: isCollapsed ? 'Expand' : 'Collapse',
      icon: isCollapsed ? 'pi pi-angle-right' : 'pi pi-angle-left',
      command: () => {
        setIsCollapsed(!isCollapsed);
        document.activeElement.blur();
      }
    }
  ];

  const megaMenuPT = {
    root: {
      className: 'h-full border-none',
    },
    menu: {
      style: { 
        marginTop: '5px',
        height: '100%',
        backgroundColor: '#f8f9fa',
        borderRadius: '0 4px 4px 0'
      },
    },
    label: {
      style: {
        transition: 'margin-right 0.3s ease, opacity 0.3s ease, max-width 0.3s ease',
        marginRight: isCollapsed ? '0px' : '10px',
        opacity: isCollapsed ? 0 : 1,
        visibility: isCollapsed ? 'hidden' : 'visible',
        whiteSpace: 'nowrap',
        maxWidth: isCollapsed ? 0 : '140px',
      },
    },
    icon: {
      style: {
        marginLeft: isCollapsed ? '0' : '20px',
        fontSize: '1.2rem'
      }
    },
    menuitem: {
      className: 'mb-2'
    }
  };

  return (
    <div 
      className="side-menu-container" 
      style={{
        position: 'fixed',
        left: 0,
        top: '60px', // Matches the height of TopMenuBar
        bottom: 0,
        width: isCollapsed ? '50px' : '200px',
        transition: 'width 0.3s ease',
        zIndex: 10, // Ensure it's below TopMenuBar (which should have higher z-index)
        overflowY: 'auto' // Add scroll if content is too long
      }}
    >
      <MegaMenu 
        model={items} 
        orientation="vertical" 
        className="h-full p-0 border-right-1 surface-border" 
        pt={megaMenuPT} 
      />
    </div>
  );
};

export default SideMenuBar;