{"name": "invoices-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@azure/msal-browser": "^4.12.0", "@azure/msal-react": "^3.0.12", "@tailwindcss/vite": "^4.1.7", "ag-grid-community": "^33.2.4", "ag-grid-react": "^33.2.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.503.0", "mobx-react": "^9.2.0", "prime-react": "^1.0.0", "primeflex": "^4.0.0", "primeicons": "^7.0.0", "primereact": "^10.9.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.6.0", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.8"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "vite": "^6.2.0"}}