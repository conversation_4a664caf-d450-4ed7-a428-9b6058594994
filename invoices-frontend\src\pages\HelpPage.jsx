import React, { useState } from 'react';
import { TabView, Tab<PERSON>anel } from 'primereact/tabview';
import { Card } from 'primereact/card';
import { Badge } from 'primereact/badge';
import GettingStarted from '../components/HelpPage/GettingStarted';
import FunctionsHelp from '../components/HelpPage/Dashboard/Functions';
import FilterBarHelp from '../components/HelpPage/Dashboard/Filter';
import EditScreenHelp from '../components/HelpPage/Dashboard/Editing';
import XRefHelp from '../components/HelpPage/Xref';
import IntegrationStatusHelp from '../components/HelpPage/Status';
import AdminHelp from '../components/HelpPage/Admin';
import InvoiceTablesHelp from '../components/HelpPage/Dashboard/Data';
import LogsHelp from '../components/HelpPage/Logs';

const HelpPage = () => {
  const [activeDashboardSubTab, setActiveDashboardSubTab] = useState(0);

  const renderDashboardContent = () => {
    switch (activeDashboardSubTab) {
      case 0: // The Data
        return (
          <Card title="Understanding Your Data" className="mt-3">
            <InvoiceTablesHelp/>
          </Card>
        );
      case 1: // Filter
        return (
          <Card title="Filtering Options" className="mt-3">
            <FilterBarHelp/>
          </Card>
        );
      case 2: // Functions
        return (
          <Card title="Available Functions" className="mt-3">
            <FunctionsHelp/>
          </Card>
        );
      case 3: // Editing
        return (
          <Card title="Editing Data" className="mt-3">
            <EditScreenHelp/>
          </Card>
        );
      default:
        return null;
    }
  };

  const dashboardTabHeader = (
    <div className="flex align-items-center">
      <i className="pi pi-chart-bar mr-2"></i>
      <span>Dashboard</span>
    </div>
  );

  return (
    <div className="min-h-screen surface-ground">
      <div className="max-w-6xl mx-auto p-4">
        <Card className="shadow-3">
          {/* Header */}
          <div className="bg-primary text-primary-50 p-4 border-round-top">
            <h1 className="text-3xl font-bold m-0">Help & Documentation</h1>
            <p className="mt-2 mb-0 text-primary-100">
              Find answers and learn how to use the application
            </p>
          </div>

          {/* Main Tab Content */}
          <div className="p-0">
            <TabView className="help-tabs">
              <TabPanel 
                header={
                  <div className="flex align-items-center">
                    <i className="pi pi-book mr-2"></i>
                    <span>Getting Started</span>
                  </div>
                }
              >
                <GettingStarted />
              </TabPanel>

              <TabPanel header={dashboardTabHeader}>
                <div className="mt-3">
                  <TabView 
                    activeIndex={activeDashboardSubTab} 
                    onTabChange={(e) => setActiveDashboardSubTab(e.index)}
                    className="dashboard-subtabs"
                  >
                    <TabPanel 
                      header={
                        <div className="flex align-items-center">
                          <i className="pi pi-database mr-2"></i>
                          <span>The Data</span>
                        </div>
                      }
                    >
                      {renderDashboardContent()}
                    </TabPanel>

                    <TabPanel 
                      header={
                        <div className="flex align-items-center">
                          <i className="pi pi-filter mr-2"></i>
                          <span>Filter</span>
                        </div>
                      }
                    >
                      {renderDashboardContent()}
                    </TabPanel>

                    <TabPanel 
                      header={
                        <div className="flex align-items-center">
                          <i className="pi pi-cog mr-2"></i>
                          <span>Functions</span>
                        </div>
                      }
                    >
                      {renderDashboardContent()}
                    </TabPanel>

                    <TabPanel 
                      header={
                        <div className="flex align-items-center">
                          <i className="pi pi-pencil mr-2"></i>
                          <span>Editing</span>
                        </div>
                      }
                    >
                      {renderDashboardContent()}
                    </TabPanel>
                  </TabView>
                </div>
              </TabPanel>

              <TabPanel 
                header={
                  <div className="flex align-items-center">
                    <i className="pi pi-shield mr-2"></i>
                    <span>Admin</span>
                  </div>
                }
              >
                <Card title="Administration" className="mt-3">
                  <AdminHelp/>
                </Card>
              </TabPanel>

              <TabPanel 
                header={
                  <div className="flex align-items-center">
                    <i className="pi pi-file-o mr-2"></i>
                    <span>Logs</span>
                  </div>
                }
              >
                <Card title="System Logs" className="mt-3">
                  <LogsHelp/>
                </Card>
              </TabPanel>

              <TabPanel 
                header={
                  <div className="flex align-items-center">
                    <i className="pi pi-heart mr-2"></i>
                    <span>Status</span>
                  </div>
                }
              >
                <Card title="System Status" className="mt-3">
                  <IntegrationStatusHelp/>
                </Card>
              </TabPanel>

              <TabPanel 
                header={
                  <div className="flex align-items-center">
                    <i className="pi pi-link mr-2"></i>
                    <span>XRef</span>
                  </div>
                }
              >
                <Card title="Cross Reference" className="mt-3">
                  <XRefHelp/>
                </Card>
              </TabPanel>
            </TabView>
          </div>
        </Card>

        {/* Footer */}
        <div className="text-center mt-4">
          <p className="text-500 text-sm m-0">
            Need additional help? Contact support or check our FAQ section.
          </p>
        </div>
      </div>

      <style jsx>{`
        .help-tabs .p-tabview-panels {
          padding: 0;
        }
        
        .dashboard-subtabs {
          background: transparent;
        }
        
        .dashboard-subtabs .p-tabview-nav {
          background: var(--surface-50);
          border-radius: 6px;
          padding: 0.25rem;
        }
        
        .dashboard-subtabs .p-tabview-panels {
          padding: 0;
          background: transparent;
        }
      `}</style>
    </div>
  );
};

export default HelpPage;