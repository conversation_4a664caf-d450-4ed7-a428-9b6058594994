// Layout.jsx
import { useState } from 'react';
import { useAuth } from '../../Auth/useAuth';
import TopMenuBar from './TopMenuBar';
import SideMenuBar from '../Sidebar';

const Layout = ({ children }) => {
  const { isAuthenticated } = useAuth();
  const [isCollapsed, setIsCollapsed] = useState(false);

  return (
    <div className="app-container">
      <TopMenuBar />
      {isAuthenticated && (
        <>
          <SideMenuBar 
            isCollapsed={isCollapsed} 
            setIsCollapsed={setIsCollapsed} 
          />
          <div 
            className="main-content" 
            style={{
              marginLeft: isCollapsed ? '50px' : '200px',
              marginTop: '60px', // Push content below TopMenuBar
              transition: 'margin-left 0.3s ease',
              minHeight: 'calc(100vh - 60px)' // Account for TopMenuBar height
            }}
          >
            {children}
          </div>
        </>
      )}
      {!isAuthenticated && (
        <div className="welcome-message">
          <h2>Welcome to our app</h2>
          <p>Please sign in to continue</p>
        </div>
      )}
    </div>
  );
};

export default Layout;