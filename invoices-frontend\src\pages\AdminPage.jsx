import React, { useState, useEffect, useRef, useCallback } from "react";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { InputText } from "primereact/inputtext";
import { InputNumber } from "primereact/inputnumber";
import { But<PERSON> } from "primereact/button";
import { Dropdown } from "primereact/dropdown";
import { Checkbox } from "primereact/checkbox";
import { Card } from "primereact/card";
import { Dialog } from "primereact/dialog";
import { Toast } from "primereact/toast";
import { ConfirmDialog, confirmDialog } from "primereact/confirmdialog";
import { Tag } from "primereact/tag";
import apiService from "../components/api/apiService";
import { useApi } from "../components/api/useApi";
import { useAuth } from "../Auth/useAuth";

export default function AdminPage() {
  const [configs, setConfigs] = useState([]);
  const [globalFilterValue, setGlobalFilterValue] = useState("");
  const [showDialog, setShowDialog] = useState(false);
  const [editingConfig, setEditingConfig] = useState(null);
  const [dialogData, setDialogData] = useState({
    configID: 0,
    counterpartyID: null,
    counterpartyName: "",
    isAutomatic: false,
    invoiceHeaderTypeID: null,
    invoiceHeaderType: "",
    defaultDueDateID: null,
    defaultDueDate: "",
    minThreshold: 0,
    maxThreshold: 0,
  });
  const [counterpartyOptions, setCounterpartyOptions] = useState([]);
  const [invoiceHeaderTypeOptions, setInvoiceHeaderTypeOptions] = useState([]);
  const [defaultDueDateOptions, setDefaultDueDateOptions] = useState([]);

  const { getAuthHeaders } = useAuth();
  const { loading: configsLoading, callApi: callConfigsApi } = useApi();
  const { loading: saveLoading, callApi: callSaveApi } = useApi();
  const { callApi: callDeleteApi } = useApi();

  const toast = useRef(null);

  const fetchConfigs = useCallback(async () => {
    try {
      const response = await callConfigsApi(() =>
        apiService.get("/Admin/GetAllConfigs", {}, getAuthHeaders)
      );
      setConfigs(response || []);
    } catch (err) {
      console.error("Error fetching configs: ", err);
      toast.current?.show({
        severity: "error",
        summary: "Error",
        detail: "Failed to fetch counterparty configurations",
      });
    }
  }, [callConfigsApi, getAuthHeaders]);

  const fetchDropdownOptions = useCallback(async () => {
    try {
      const counterpartyResponse = await callConfigsApi(() =>
        apiService.get("/Lookups/GetBAs", {}, getAuthHeaders)
      );
      const counterpartyOpts = (counterpartyResponse || []).map((cp) => ({
        label: cp.value,
        value: cp.id,
      }));
      setCounterpartyOptions(counterpartyOpts);

      const headerTypeResponse = await callConfigsApi(() =>
        apiService.get("/Lookups/GetTemplates", {}, getAuthHeaders)
      );
      const headerTypeOpts = (headerTypeResponse || []).map((ht) => ({
        label: ht.value,
        value: ht.id,
      }));
      setInvoiceHeaderTypeOptions(headerTypeOpts);

      const dueDateResponse = await callConfigsApi(() =>
        apiService.get("/Lookups/GetDueTerms", {}, getAuthHeaders)
      );
      const dueDateOpts = (dueDateResponse || []).map((ht) => ({
        label: ht.value,
        value: ht.id,
      }));
      setDefaultDueDateOptions(dueDateOpts);
    } catch (err) {
      console.error("Error fetching dropdown options: ", err);
    }
  }, [callConfigsApi, getAuthHeaders]);

  useEffect(() => {
    fetchConfigs();
    fetchDropdownOptions();
  }, [fetchConfigs, fetchDropdownOptions]);


  const onGlobalFilterChange = (e) => {
    setGlobalFilterValue(e.target.value);
  };

  const openAddDialog = () => {
    setEditingConfig(null);
    setDialogData({
      configID: 0,
      counterpartyID: null,
      counterpartyName: '',
      isAutomatic: false,
      invoiceHeaderTypeID: null,
      invoiceHeaderType: '',
      defaultDueDateID: null,
      defaultDueDate: '',
      minThreshold: 0,
      maxThreshold: 0
    });
    setShowDialog(true);
  };

  const openEditDialog = (config) => {
    setEditingConfig(config);
    setDialogData({ ...config });
    setShowDialog(true);
  };

  const handleSave = async () => {
    if (!dialogData.counterpartyID || !dialogData.invoiceHeaderTypeID) {
      toast.current?.show({severity: 'warn', summary: 'Warning', detail: 'Please select Counterparty and Invoice Header Type'});
      return;
    }

    try {
      // Get the display names for the selected options
      const selectedCounterparty = counterpartyOptions.find(cp => cp.value === dialogData.counterpartyID);
      const selectedHeaderType = invoiceHeaderTypeOptions.find(ht => ht.value === dialogData.invoiceHeaderTypeID);
      const selectedDueDate = defaultDueDateOptions.find(dd => dd.value === dialogData.defaultDueDateID);

      const saveData = {
        ...dialogData,
        counterpartyID: Number(dialogData.counterpartyID),
        counterpartyName: selectedCounterparty?.label || '',
        invoiceHeaderTypeID: Number(dialogData.invoiceHeaderTypeID),
        invoiceHeaderType: selectedHeaderType?.label || '',
        defaultDueDateID: dialogData.defaultDueDateID ? Number(dialogData.defaultDueDateID) : null,
        defaultDueDate: selectedDueDate?.label || '',
        minThreshold: Number(dialogData.minThreshold) || 0,
        maxThreshold: Number(dialogData.maxThreshold) || 0
      };

      if (editingConfig) {
        await callSaveApi(() => 
          apiService.put('/Admin/UpdateConfig', saveData, {}, getAuthHeaders)
        );
        toast.current?.show({severity: 'success', summary: 'Success', detail: 'Configuration updated successfully'});
      } else {
        await callSaveApi(() => 
          apiService.post('/Admin/CreateConfig', saveData, {}, getAuthHeaders)
        );
        toast.current?.show({severity: 'success', summary: 'Success', detail: 'Configuration created successfully'});
      }
      
      setShowDialog(false);
      await fetchConfigs();
    } catch (err) {
      console.error("Error saving config: ", err);
      toast.current?.show({severity: 'error', summary: 'Error', detail: 'Failed to save configuration'});
    }
  };

  const handleDelete = (config) => {
    confirmDialog({
      message: `Are you sure you want to delete the configuration for "${config.counterpartyName}"?`,
      header: 'Confirm Delete',
      icon: 'pi pi-exclamation-triangle',
      accept: async () => {
        try {
          await callDeleteApi(() => 
            apiService.delete('/Admin/DeleteConfig', config, {}, getAuthHeaders)
          );
          toast.current?.show({severity: 'success', summary: 'Success', detail: 'Configuration deleted successfully'});
          await fetchConfigs();
        } catch (err) {
          console.error("Error deleting config: ", err);
          toast.current?.show({severity: 'error', summary: 'Error', detail: 'Failed to delete configuration'});
        }
      }
    });
  };

  // Template for boolean values
  const booleanBodyTemplate = (rowData, field) => {
    return (
      <Tag 
        value={rowData[field] ? 'Yes' : 'No'} 
        severity={rowData[field] ? 'success' : 'danger'}
      />
    );
  };

  // Template for currency values
  const currencyBodyTemplate = (value) => {
    if (value === null || value === undefined) return 'N/A';
    return new Intl.NumberFormat('en-US', { 
      style: 'currency', 
      currency: 'USD' 
    }).format(value);
  };

  const actionBodyTemplate = (rowData) => {
    return (
      <div className="flex gap-2">
        <Button 
          icon="pi pi-pencil" 
          rounded 
          outlined 
          size="small"
          onClick={() => openEditDialog(rowData)}
          tooltip="Edit"
        />
        <Button 
          icon="pi pi-trash" 
          rounded 
          outlined 
          severity="danger" 
          size="small"
          onClick={() => handleDelete(rowData)}
          tooltip="Delete"
        />
      </div>
    );
  };

  const renderHeader = () => {
    return (
      <div className="flex justify-content-between align-items-center">
        <h2 className="m-0">Counterparty Configurations</h2>
        <div className="flex gap-2">
          <span className="p-input-icon-left">
            <i className="pi pi-search" style={{paddingLeft: '0.5rem'}} />
            <InputText
              value={globalFilterValue}
              onChange={onGlobalFilterChange}
              placeholder="Search configurations..."
              style={{ paddingLeft: '2.5rem' }}
            />
          </span>
          <Button
            type="button"
            icon="pi pi-plus"
            label="Add Config"
            onClick={openAddDialog}
          />
          <Button
            type="button"
            icon="pi pi-refresh"
            label="Refresh"
            outlined
            onClick={fetchConfigs}
            loading={configsLoading}
          />
        </div>
      </div>
    );
  };

  return (
    <div className="p-6">
      <Toast ref={toast} />
      <ConfirmDialog />
      
      <Card>
        <DataTable
          value={configs}
          loading={configsLoading}
          header={renderHeader()}
          globalFilter={globalFilterValue}
          globalFilterFields={['counterpartyName', 'invoiceHeaderType', 'defaultDueDate']}
          emptyMessage={configsLoading ? "Loading configurations..." : "No configurations found"}
          paginator
          rows={25}
          rowsPerPageOptions={[10, 25, 50, 100]}
          sortMode="multiple"
          removableSort
          scrollable
          scrollHeight="calc(100vh - 300px)"
          showGridlines
          stripedRows
          resizableColumns
          columnResizeMode="expand"
        >
          <Column 
            field="counterpartyName"
            header="Counterparty"
            sortable
            style={{ minWidth: '200px' }}
          />
          <Column 
            field="invoiceHeaderType"
            header="Header Type"
            sortable
            style={{ minWidth: '150px' }}
          />
          <Column 
            field="isAutomatic"
            header="Automatic"
            body={(rowData) => booleanBodyTemplate(rowData, 'isAutomatic')}
            sortable
            style={{ minWidth: '120px' }}
          />
          <Column 
            field="defaultDueDate"
            header="Due Date Terms"
            sortable
            style={{ minWidth: '130px' }}
          />
          <Column 
            field="minThreshold"
            header="Min Threshold"
            body={(rowData) => currencyBodyTemplate(rowData.minThreshold)}
            sortable
            style={{ minWidth: '130px' }}
          />
          <Column 
            field="maxThreshold"
            header="Max Threshold"
            body={(rowData) => currencyBodyTemplate(rowData.maxThreshold)}
            sortable
            style={{ minWidth: '130px' }}
          />
          <Column
            header="Actions"
            body={actionBodyTemplate}
            style={{ minWidth: '120px', textAlign: 'center' }}
          />
        </DataTable>
      </Card>

      <Dialog
        header={editingConfig ? "Edit Configuration" : "Add New Configuration"}
        visible={showDialog}
        onHide={() => setShowDialog(false)}
        style={{ width: "800px", maxHeight: "90vh" }}
        modal
      >
        <div className="flex flex-column gap-4">
          <div className="grid">
            <div className="col-12 md:col-6">
              <div className="flex flex-column gap-2">
                <label htmlFor="counterparty">Counterparty *</label>
                <Dropdown
                  id="counterparty"
                  value={dialogData.counterpartyID}
                  options={counterpartyOptions}
                  onChange={(e) => setDialogData(prev => ({ ...prev, counterpartyID: e.value }))}
                  placeholder="Select Counterparty"
                  className="w-full"
                  filter
                  disabled={editingConfig !== null}
                />
              </div>
            </div>
            
            <div className="col-12 md:col-6">
              <div className="flex flex-column gap-2">
                <label htmlFor="headerType">Invoice Header Type *</label>
                <Dropdown
                  id="headerType"
                  value={dialogData.invoiceHeaderTypeID}
                  options={invoiceHeaderTypeOptions}
                  onChange={(e) => setDialogData(prev => ({ ...prev, invoiceHeaderTypeID: e.value }))}
                  placeholder="Select Header Type"
                  className="w-full"
                />
              </div>
            </div>
          </div>

          <div className="grid">
            <div className="col-12 md:col-4">
              <div className="flex flex-column gap-2">
                <label htmlFor="dueDate">Default Due Date</label>
                <Dropdown
                  id="dueDate"
                  value={dialogData.defaultDueDateID}
                  options={defaultDueDateOptions}
                  onChange={(e) => setDialogData(prev => ({ ...prev, defaultDueDateID: e.value }))}
                  placeholder="Select Due Date Terms"
                  className="w-full"
                />
              </div>
            </div>
            
            <div className="col-12 md:col-4">
              <div className="flex flex-column gap-2">
                <label htmlFor="minThreshold">Min Threshold</label>
                <InputNumber
                  id="minThreshold"
                  value={dialogData.minThreshold}
                  onValueChange={(e) => setDialogData(prev => ({ ...prev, minThreshold: e.value }))}
                  mode="currency"
                  currency="USD"
                  locale="en-US"
                  min={0}
                  className="w-full"
                />
              </div>
            </div>
            
            <div className="col-12 md:col-4">
              <div className="flex flex-column gap-2">
                <label htmlFor="maxThreshold">Max Threshold</label>
                <InputNumber
                  id="maxThreshold"
                  value={dialogData.maxThreshold}
                  onValueChange={(e) => setDialogData(prev => ({ ...prev, maxThreshold: e.value }))}
                  mode="currency"
                  currency="USD"
                  locale="en-US"
                  min={0}
                  className="w-full"
                />
              </div>
            </div>
          </div>

          <div className="flex align-items-center gap-2">
            <Checkbox
              id="isAutomatic"
              checked={dialogData.isAutomatic}
              onChange={(e) => setDialogData(prev => ({ ...prev, isAutomatic: e.checked }))}
            />
            <label htmlFor="isAutomatic">Automatic Processing</label>
          </div>
          
          <div className="flex justify-content-end gap-2 mt-3">
            <Button
              label="Cancel"
              outlined
              onClick={() => setShowDialog(false)}
            />
            <Button
              label={editingConfig ? "Update" : "Create"}
              onClick={handleSave}
              loading={saveLoading}
            />
          </div>
        </div>
      </Dialog>
    </div>
  );
}