import React, { useState, useEffect, useCallback } from "react";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { InputText } from "primereact/inputtext";
import { But<PERSON> } from "primereact/button";
import { Dropdown } from "primereact/dropdown";
import { Card } from "primereact/card";
import { Dialog } from "primereact/dialog";
import { Toast } from "primereact/toast";
import { ConfirmDialog, confirmDialog } from "primereact/confirmdialog";
import "primereact/resources/themes/lara-light-indigo/theme.css";
import "primereact/resources/primereact.min.css";
import apiService from "../components/api/apiService";
import { useApi } from "../components/api/useApi";
import { useAuth } from "../Auth/useAuth";

export default function XRefPage() {
  const [xrefs, setXrefs] = useState([]);
  const [filteredXrefs, setFilteredXrefs] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('');
  const [categories, setCategories] = useState([]);
  const [globalFilterValue, setGlobalFilterValue] = useState('');
  const [showDialog, setShowDialog] = useState(false);
  const [editingXref, setEditingXref] = useState(null);
  const [dialogData, setDialogData] = useState({
    id: 0,
    type: '',
    input: '',
    output: '',
    inputID: null,
    outputID: null
  });
  const [inputOptions, setInputOptions] = useState([]);
  const [outputOptions, setOutputOptions] = useState([]);
  const [inputIsDropdown, setInputIsDropdown] = useState(false);
  const [outputIsDropdown, setOutputIsDropdown] = useState(false);
  
  const { getAuthHeaders } = useAuth();
  const { loading: xrefsLoading, callApi: callXrefsApi } = useApi();
  const { loading: saveLoading, callApi: callSaveApi } = useApi();
  const { loading: deleteLoading, callApi: callDeleteApi } = useApi();
  
  const toast = React.useRef(null);

  const fetchXrefs = useCallback(async () => {
    try {
      const response = await callXrefsApi(() => 
        apiService.get('/XRef/GetAllXrefs', {}, getAuthHeaders)
      );
      setXrefs(response || []);
    } catch (err) {
      console.error("Error fetching xrefs: ", err);
      toast.current?.show({severity: 'error', summary: 'Error', detail: 'Failed to fetch XRefs'});
    }
  }, [callXrefsApi, getAuthHeaders]);

  const fetchCategories = useCallback(async () => {
    try {
      const response = await callXrefsApi(() => 
        apiService.get('/XRef/GetCategories', {}, getAuthHeaders)
      );
      const categoryOptions = (response || []).map(cat => ({ label: cat, value: cat }));
      setCategories([{ label: 'All Categories', value: '' }, ...categoryOptions]);
    } catch (err) {
      console.error("Error fetching categories: ", err);
    }
  }, [callXrefsApi, getAuthHeaders]);

  const filterXrefsByCategory = useCallback(() => {
    if (!selectedCategory) {
      setFilteredXrefs(xrefs);
    } else {
      setFilteredXrefs(xrefs.filter(xref => xref.type === selectedCategory));
    }
  }, [selectedCategory, xrefs]);

  useEffect(() => {
    const fetchData = async () => {
      await fetchXrefs();
      await fetchCategories();
    };
    fetchData();
  }, [fetchXrefs, fetchCategories]);

  useEffect(() => {
    filterXrefsByCategory();
  }, [filterXrefsByCategory]);

  const fetchDropdownOptions = async (type, field) => {
    try {
      const response = await callXrefsApi(() => 
        apiService.get(`/XRef/GetOptions/${type}/${field}`, {}, getAuthHeaders)
      );
      
      // API returns null when it should be a text input, array of LookupItemDTO when dropdown
      if (response === null) {
        return null; // Indicates text input should be used
      }
      
      // Convert LookupItemDTO array to dropdown options
      return (response || []).map(item => ({ 
        label: item.value, 
        value: item.id 
      }));
    } catch (err) {
      console.error(`Error fetching ${field} options: `, err);
      return null;
    }
  };

  const onGlobalFilterChange = (e) => {
    setGlobalFilterValue(e.target.value);
  };

  const openAddDialog = async () => {
    setEditingXref(null);
    const initialType = selectedCategory || '';
    setDialogData({
      id: 0,
      type: initialType,
      input: '',
      output: '',
      inputID: null,
      outputID: null
    });
    setInputOptions([]);
    setOutputOptions([]);
    setInputIsDropdown(false);
    setOutputIsDropdown(false);
    
    // Load field options if a category is pre-selected
    if (initialType) {
      await loadFieldOptions(initialType);
    }
    
    setShowDialog(true);
  };

  const openEditDialog = async (xref) => {
    setEditingXref(xref);
    setDialogData({ ...xref });
    
    // Load dropdown options and determine field types
    await loadFieldOptions(xref.type);
    
    setShowDialog(true);
  };

  const loadFieldOptions = async (type) => {
    if (!type) {
      setInputOptions([]);
      setOutputOptions([]);
      setInputIsDropdown(false);
      setOutputIsDropdown(false);
      return;
    }

    // Load input options
    const inputOpts = await fetchDropdownOptions(type, 'input');
    if (inputOpts === null) {
      setInputIsDropdown(false);
      setInputOptions([]);
    } else {
      setInputIsDropdown(true);
      setInputOptions(inputOpts);
    }

    // Load output options
    const outputOpts = await fetchDropdownOptions(type, 'output');
    if (outputOpts === null) {
      setOutputIsDropdown(false);
      setOutputOptions([]);
    } else {
      setOutputIsDropdown(true);
      setOutputOptions(outputOpts);
    }
  };

  const handleTypeChange = async (newType) => {
    setDialogData(prev => ({ 
      ...prev, 
      type: newType, 
      input: '', 
      output: '',
      inputID: null,
      outputID: null
    }));
    await loadFieldOptions(newType);
  };

  const handleSave = async () => {
    if (!dialogData.type || !dialogData.input || !dialogData.output) {
      toast.current?.show({severity: 'warn', summary: 'Warning', detail: 'Please fill all fields'});
      return;
    }

    try {
      // Prepare the data for saving - use IDs when available for dropdown fields
      // Ensure all values are converted to strings
      const saveData = {
        ...dialogData,
        input: String(inputIsDropdown && dialogData.inputID ? dialogData.inputID : dialogData.input),
        output: String(outputIsDropdown && dialogData.outputID ? dialogData.outputID : dialogData.output)
      };

      if (editingXref) {
        await callSaveApi(() => 
          apiService.put('/XRef/UpdateXref', saveData, {}, getAuthHeaders)
        );
        toast.current?.show({severity: 'success', summary: 'Success', detail: 'XRef updated successfully'});
      } else {
        await callSaveApi(() => 
          apiService.post('/XRef/CreateXref', saveData, {}, getAuthHeaders)
        );
        toast.current?.show({severity: 'success', summary: 'Success', detail: 'XRef created successfully'});
      }
      
      setShowDialog(false);
      await fetchXrefs();
    } catch (err) {
      console.error("Error saving xref: ", err);
      toast.current?.show({severity: 'error', summary: 'Error', detail: 'Failed to save XRef'});
    }
  };

  const handleDelete = (xref) => {
    confirmDialog({
      message: `Are you sure you want to delete this XRef mapping: "${xref.input}" → "${xref.output}"?`,
      header: 'Confirm Delete',
      icon: 'pi pi-exclamation-triangle',
      accept: async () => {
        try {
          await callDeleteApi(() => 
            apiService.delete('/XRef/DeleteXref', xref, {}, getAuthHeaders)
          );
          toast.current?.show({severity: 'success', summary: 'Success', detail: 'XRef deleted successfully'});
          await fetchXrefs();
        } catch (err) {
          console.error("Error deleting xref: ", err);
          toast.current?.show({severity: 'error', summary: 'Error', detail: 'Failed to delete XRef'});
        }
      }
    });
  };

  const actionBodyTemplate = (rowData) => {
    return (
      <div className="flex gap-1">
        <Button 
          icon="pi pi-pencil" 
          rounded 
          outlined 
          size="small"
          onClick={() => openEditDialog(rowData)}
          tooltip="Edit"
        />
        <Button 
          icon="pi pi-trash" 
          rounded 
          outlined 
          severity="danger" 
          size="small"
          onClick={() => handleDelete(rowData)}
          tooltip="Delete"
          loading={deleteLoading}
        />
      </div>
    );
  };

  const renderHeader = () => {
    return (
      <div className="flex justify-content-between align-items-center">
        <h2 className="m-0">Cross References</h2>
        <div className="flex gap-2 align-items-center">
          <Dropdown
            value={selectedCategory}
            options={categories}
            onChange={(e) => setSelectedCategory(e.value)}
            placeholder="Select Category"
            style={{ minWidth: '200px' }}
          />
          <span className="p-input-icon-left">
            <i className="pi pi-search" style = {{paddingLeft: '0.5rem'}}/>
            <InputText
              value={globalFilterValue}
              onChange={onGlobalFilterChange}
              placeholder="Search XRefs..."
              style={{ paddingLeft: '2.5rem' }}
            />
          </span>
          <Button
            type="button"
            icon="pi pi-plus"
            label="Add XRef"
            onClick={openAddDialog}
          />
          <Button
            type="button"
            icon="pi pi-refresh"
            label="Refresh"
            outlined
            onClick={fetchXrefs}
            loading={xrefsLoading}
          />
        </div>
      </div>
    );
  };

  const renderInputField = () => {
    if (inputIsDropdown) {
      return (
        <Dropdown
          value={dialogData.inputID ? Number(dialogData.inputID) : null}
          options={inputOptions}
          onChange={(e) =>
            setDialogData((prev) => ({
              ...prev,
              inputID: e.value,
              input: inputOptions.find((opt) => opt.value === e.value)?.label || ''
            }))
          }
          placeholder="Select Input"
          className="w-full"
        />
      );
    }
    return (
      <InputText
        value={dialogData.input}
        onChange={(e) => setDialogData(prev => ({ 
          ...prev, 
          input: e.target.value,
          inputID: null
        }))}
        placeholder="Enter Input"
        className="w-full"
      />
    );
  };

  const renderOutputField = () => {
    if (outputIsDropdown) {
      return (
        <Dropdown
          value={dialogData.outputID ? Number(dialogData.outputID) : null}
          options={outputOptions}
          onChange={(e) =>
            setDialogData((prev) => ({
              ...prev,
              outputID: e.value,
              output: outputOptions.find((opt) => opt.value === e.value)?.label || ''
            }))
          }
          placeholder="Select Output"
          className="w-full"
        />
      );
    }
    return (
      <InputText
        value={dialogData.output}
        onChange={(e) => setDialogData(prev => ({ 
          ...prev, 
          output: e.target.value,
          outputID: null
        }))}
        placeholder="Enter Output"
        className="w-full"
      />
    );
  };

  return (
    <div className="p-6">
      <Toast ref={toast} />
      <ConfirmDialog />
      
      {/* Custom CSS to make table rows thinner */}
      <style>
        {`
          .p-datatable .p-datatable-tbody > tr {
            height: 36px !important;
          }
          .p-datatable .p-datatable-tbody > tr > td {
            padding: 0.5rem !important;
            vertical-align: middle !important;
          }
          .p-datatable .p-datatable-thead > tr > th {
            padding: 0.75rem 0.5rem !important;
          }
        `}
      </style>
      
      <Card>
        <DataTable
          value={filteredXrefs}
          loading={xrefsLoading}
          header={renderHeader()}
          globalFilter={globalFilterValue}
          globalFilterFields={['input', 'output', 'type']}
          emptyMessage={xrefsLoading ? "Loading XRefs..." : "No XRefs found"}
          paginator
          rows={25}
          rowsPerPageOptions={[10, 25, 50, 100]}
          sortMode="multiple"
          removableSort
          scrollable
          scrollHeight="calc(100vh - 300px)"
          showGridlines
          stripedRows
          size="small"
        >
          <Column 
            field="type"
            header="Category"
            sortable
            style={{ minWidth: '150px' }}
          />
          <Column 
            field="input"
            header="Input"
            sortable
            style={{ minWidth: '200px' }}
          />
          <Column 
            field="output"
            header="Output"
            sortable
            style={{ minWidth: '200px' }}
          />
          <Column
            header="Actions"
            body={actionBodyTemplate}
            style={{ minWidth: '120px', textAlign: 'center' }}
          />
        </DataTable>
      </Card>

      <Dialog
        header={editingXref ? "Edit XRef" : "Add New XRef"}
        visible={showDialog}
        onHide={() => setShowDialog(false)}
        style={{ width: "500px" }}
        modal
      >
        <div className="flex flex-column gap-3">
          <div className="flex flex-column gap-2">
            <label htmlFor="type">Category</label>
            <Dropdown
              id="type"
              value={dialogData.type}
              options={categories.filter(cat => cat.value !== '')}
              onChange={(e) => handleTypeChange(e.value)}
              placeholder="Select Category"
              className="w-full"
            />
          </div>
          
          <div className="flex flex-column gap-2">
            <label htmlFor="input">Input</label>
            {renderInputField()}
          </div>
          
          <div className="flex flex-column gap-2">
            <label htmlFor="output">Output</label>
            {renderOutputField()}
          </div>
          
          <div className="flex justify-content-end gap-2 mt-3">
            <Button
              label="Cancel"
              outlined
              onClick={() => setShowDialog(false)}
            />
            <Button
              label={editingXref ? "Update" : "Create"}
              onClick={handleSave}
              loading={saveLoading}
            />
          </div>
        </div>
      </Dialog>
    </div>
  );
}