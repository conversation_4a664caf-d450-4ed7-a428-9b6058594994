// src/Auth/msalInstance.js
import { PublicClientApplication } from "@azure/msal-browser";
import { msalConfig } from "./authConfig"; // Ensure this path is correct

let msalAppInstance = null;

if (typeof window !== "undefined" && window.crypto?.subtle) {
    msalAppInstance = new PublicClientApplication(msalConfig);
} else {
    console.error(
        "MSAL could not be initialized: window.crypto.subtle is not available. " +
        "Ensure you are running in a secure context (HTTPS or localhost)."
    );
    // You might want to throw an error here or handle this state appropriately in your app
}

export const msalApp = msalAppInstance;